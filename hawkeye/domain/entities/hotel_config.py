from hawkeye.constants.hawkeye_constant import HotelStatus
from hawkeye.infrastructure.base_entity import EntityChangeTracker


class HotelConfig(EntityChangeTracker):
    """
    hotel config
    """

    def __init__(
            self, hotel_id, is_enabled=False, is_live=False, is_competitor_price_enabled=False,
            revenue_poc_emails=None, new=True, dirty=True, file_id=None
    ):
        super().__init__(new, dirty)
        self.hotel_id = hotel_id
        self.is_enabled = is_enabled
        self.is_live = is_live
        self.is_competitor_price_enabled = is_competitor_price_enabled
        self.revenue_poc_emails = revenue_poc_emails
        self.file_id = file_id

    def update_status_from_dto(self, hotel_dto):
        self.is_live = True if hotel_dto.status == HotelStatus.LIVE else False
