from treebo_commons.utils.dateutils import add, current_datetime

from hawkeye.infrastructure.base_entity import EntityChangeTracker


class CompetitorHotelPricing(EntityChangeTracker):
    def __init__(
        self,
        room_type,
        price,
        competitor_hotel_id,
        stay_date,
        expires_at=None,
        id=None,
        new=True,
        dirty=False,
    ):
        super().__init__(new, dirty)
        self.id = id
        self.room_type = room_type
        self.price = price
        self.competitor_hotel_id = competitor_hotel_id
        self.stay_date = stay_date
        self.expires_at = expires_at or add(current_datetime(), days=2)

    def update_price(self, new_price):
        if self.price != new_price:
            self.price = new_price
            self.mark_dirty()

    def mark_expired(self):
        self.expires_at = current_datetime()
        self.mark_dirty()

    def is_expired(self):
        return current_datetime() > self.expires_at

    def mark_dirty(self):
        self.dirty = True
