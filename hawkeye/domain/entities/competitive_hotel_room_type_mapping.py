from hawkeye.infrastructure.base_entity import EntityChangeTracker


class CompetitiveHotelRoomTypeMapping(EntityChangeTracker):
    def __init__(
        self,
        competitive_hotel_id,
        competitive_hotel_room_type,
        internal_hotel_id,
        internal_hotel_room_type,
        is_active=True,
        file_id=None,
        id=None,
        new=True,
        dirty=False,
    ):
        super().__init__(new, dirty)
        self.id = id
        self.competitive_hotel_id = competitive_hotel_id
        self.competitive_hotel_room_type = competitive_hotel_room_type
        self.internal_hotel_id = internal_hotel_id
        self.internal_hotel_room_type = internal_hotel_room_type
        self.is_active = is_active
        self.file_id = file_id

    def mark_dirty(self):
        self.dirty = True

    def mark_inactive(self):
        self.is_active = False
        self.mark_dirty()
