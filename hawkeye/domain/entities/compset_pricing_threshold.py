from treebo_commons.utils.dateutils import ymd_str_to_date

from hawkeye.infrastructure.base_entity import EntityChangeTracker


class CompsetPricingThreshold(EntityChangeTracker):
    def __init__(
        self,
        hotel_id,
        stay_date,
        increased_percentage_threshold,
        decreased_percentage_threshold,
        normalized_competitive_price_avg,
        file_id=None,
        is_deleted=False,
        id=None,
        new=True,
        dirty=False,
    ):
        super().__init__(new, dirty)
        self.id = id
        self.hotel_id = hotel_id
        self.stay_date = stay_date
        self.increased_percentage_threshold = increased_percentage_threshold
        self.decreased_percentage_threshold = decreased_percentage_threshold
        self.normalized_competitive_price_avg = normalized_competitive_price_avg
        self.file_id = file_id
        self.is_deleted = is_deleted

    def to_dict(self):
        return dict(
            id=self.id,
            hotel_id=self.hotel_id,
            stay_date=str(self.stay_date),
            increased_percentage_threshold=self.increased_percentage_threshold,
            decreased_percentage_threshold=self.decreased_percentage_threshold,
            normalized_competitive_price_avg=self.normalized_competitive_price_avg,
        )

    def to_json(self):
        return self.to_dict()

    @staticmethod
    def from_json(compset_pricing_threshold):
        return CompsetPricingThreshold(
            id=compset_pricing_threshold["id"],
            hotel_id=compset_pricing_threshold["hotel_id"],
            stay_date=ymd_str_to_date(compset_pricing_threshold["stay_date"]),
            increased_percentage_threshold=compset_pricing_threshold[
                "increased_percentage_threshold"
            ],
            decreased_percentage_threshold=compset_pricing_threshold[
                "decreased_percentage_threshold"
            ],
            normalized_competitive_price_avg=compset_pricing_threshold[
                "normalized_competitive_price_avg"
            ],
        )

    def update_increased_percentage_threshold(self, increased_percentage_threshold):
        self.increased_percentage_threshold = increased_percentage_threshold
        self.mark_dirty()

    def update_decreased_percentage_threshold(self, decreased_percentage_threshold):
        self.decreased_percentage_threshold = decreased_percentage_threshold
        self.mark_dirty()

    def mark_deleted(self):
        self.is_deleted = True
        self.mark_dirty()

    def mark_dirty(self):
        self.dirty = True
