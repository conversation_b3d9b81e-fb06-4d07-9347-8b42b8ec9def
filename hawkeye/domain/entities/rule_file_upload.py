from hawkeye.constants.hawkeye_constant import RuleFileUploadStatus
from hawkeye.infrastructure.base_entity import EntityChangeTracker


class RuleFileUpload(EntityChangeTracker):
    """
    Price Update config
    """

    def __init__(
        self,
        file_name,
        path,
        status: RuleFileUploadStatus,
        file_id=None,
        uploaded_by=None,
        modified_at=None,
        uploaded_at=None,
        new=True,
        dirty=True,
    ):
        super().__init__(new, dirty)
        self.file_id = file_id
        self.file_name = file_name
        self.path = path
        self.uploaded_by = uploaded_by
        self.uploaded_at = uploaded_at
        self.status = status
        self.modified_at = modified_at

    def mark_success(self):
        self.status = RuleFileUploadStatus.SUCCESS

    def mark_failed(self):
        self.status = RuleFileUploadStatus.FAILED
