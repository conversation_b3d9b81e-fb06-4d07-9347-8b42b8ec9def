from hawkeye.infrastructure.base_entity import EntityChangeTracker


class IncrementalPriceRule(EntityChangeTracker):
    """
    Price Update config
    """

    def __init__(
        self,
        hotel_id,
        room_type,
        factor,
        sum_factor,
        is_deleted,
        rule_id=None,
        new=True,
        dirty=True,
    ):
        super().__init__(new, dirty)
        self.rule_id = rule_id
        self.hotel_id = hotel_id
        self.room_type = room_type
        self.factor = factor
        self.sum_factor = sum_factor
        self.is_deleted = is_deleted

    def to_json(self):
        return dict(
            rule_id=self.rule_id,
            hotel_id=self.hotel_id,
            room_type=self.room_type,
            factor=self.factor,
            sum_factor=self.sum_factor,
        )

    @staticmethod
    def from_json(room_type_price_rule):
        return IncrementalPriceRule(
            rule_id=room_type_price_rule["rule_id"],
            hotel_id=room_type_price_rule["hotel_id"],
            room_type=room_type_price_rule["room_type"],
            factor=room_type_price_rule["factor"],
            sum_factor=room_type_price_rule["sum_factor"],
            is_deleted=room_type_price_rule.get("is_deleted", False),
        )
