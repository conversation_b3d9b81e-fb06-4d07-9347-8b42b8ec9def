from hawkeye.infrastructure.base_entity import EntityChangeTracker


class RateInfoEntity(EntityChangeTracker):

    def __init__(
        self,
        rate_info_id,
        start_time,
        rate,
        channel_id,
        room_name,
        room_id,
        check_in,
        hotel_id,
        check_out,
        on_site_rate,
        net_rate,
        currency,
        rate_description,
        rate_type,
        source_url,
        max_occupancy,
        is_promo,
        closed,
        discount,
        promo_name,
        status_code,
        tax_status,
        tax_type,
        tax_amount,
        meal_inclusion_details,
        meal_inclusion_type,
        room_amenities,
        meal_plan_code=0,
        mlos=1,
        id=None,
        insert_time=None,
        scraping_time=None,
        is_active=True,
        new=True,
        dirty=True,
        **kwargs
    ):
        super().__init__(new, dirty)
        self.id = id
        self.rate_info_id = rate_info_id
        self.start_time = start_time
        self.rate = rate
        self.channel_id = channel_id
        self.room_name = room_name
        self.room_id = room_id
        self.check_in = check_in
        self.hotel_id = hotel_id
        self.check_out = check_out
        self.on_site_rate = on_site_rate
        self.net_rate = net_rate
        self.currency = currency
        self.rate_description = rate_description
        self.rate_type = rate_type
        self.source_url = source_url
        self.max_occupancy = max_occupancy
        self.is_promo = is_promo
        self.closed = closed
        self.discount = discount
        self.promo_name = promo_name
        self.status_code = status_code
        self.tax_status = tax_status
        self.tax_type = tax_type
        self.tax_amount = tax_amount
        self.meal_inclusion_details = meal_inclusion_details
        self.meal_inclusion_type = meal_inclusion_type
        self.room_amenities = room_amenities
        self.meal_plan_code = meal_plan_code
        self.mlos = mlos
        self.insert_time = insert_time
        self.scraping_time = scraping_time
        self.is_active = is_active
