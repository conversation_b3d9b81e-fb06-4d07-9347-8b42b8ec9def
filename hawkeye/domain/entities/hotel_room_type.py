from hawkeye.common.slack_alert_helpers import Slack<PERSON>lert
from hawkeye.infrastructure.base_entity import EntityChangeTracker


class HotelRoomType(EntityChangeTracker):
    """
    hotel room type
    """

    def __init__(
        self,
        hotel_id,
        room_type_code,
        room_type_name,
        total_rooms,
        id=None,
        is_base_room_type=False,
        is_active=True,
        rack_rate=None,
        max_adults=None,
        new=True,
        dirty=True,
    ):
        super().__init__(new, dirty)
        self.hotel_id = hotel_id
        self.room_type_code = room_type_code
        self.room_type_name = room_type_name
        self.total_rooms = total_rooms
        self.id = id
        self.is_base_room_type = is_base_room_type
        self.is_active = is_active
        self.rack_rate = rack_rate
        self.max_adults = max_adults

    def update_room_config(self, room_type_config_dto):
        if self.total_rooms == 0 and room_type_config_dto.total_rooms > 0:
            self.is_active = True
        elif room_type_config_dto.total_rooms == 0 and not self.is_base_room_type:
            self.is_active = False
        elif room_type_config_dto.total_rooms == 0 and self.is_base_room_type:
            SlackAlert.send_alert(f"Received event for hotel_id = {self.hotel_id} where total_rooms=0 for room_type = "
                                  f"{self.room_type_name} and is_base_room_type=True. Please Check!")
        self.total_rooms = room_type_config_dto.total_rooms
        self.max_adults = room_type_config_dto.max_adults

    def update_rack_rate(self, rack_rate):
        self.rack_rate = rack_rate
