from hawkeye.infrastructure.base_entity import EntityChangeTracker


class CompetitivePriceWeightage(EntityChangeTracker):
    def __init__(
        self,
        hotel_id,
        occupancy_start,
        occupancy_end,
        lower_competitive_price_multiplier,
        higher_competitive_price_multiplier,
        file_id=None,
        is_deleted=False,
        id=None,
        new=True,
        dirty=True,
    ):
        super().__init__(new, dirty)
        self.id = id
        self.hotel_id = hotel_id
        self.occupancy_start = occupancy_start
        self.occupancy_end = occupancy_end
        self.lower_competitive_price_multiplier = lower_competitive_price_multiplier
        self.higher_competitive_price_multiplier = higher_competitive_price_multiplier
        self.file_id = file_id
        self.is_deleted = is_deleted

    def mark_deleted(self):
        self.is_deleted = True
        self.mark_dirty()

    def mark_dirty(self):
        self.dirty = True

    def to_dict(self):
        return dict(
            id=self.id,
            hotel_id=self.hotel_id,
            occupancy_start=self.occupancy_start,
            occupancy_end=self.occupancy_end,
            lower_competitive_price_multiplier=self.lower_competitive_price_multiplier,
            higher_competitive_price_multiplier=self.higher_competitive_price_multiplier,
            file_id=self.file_id,
            is_deleted=self.is_deleted,
        )

    def to_json(self):
        return self.to_dict()

    @staticmethod
    def from_json(competitive_price_weightage):
        return CompetitivePriceWeightage(
            id=competitive_price_weightage["id"],
            hotel_id=competitive_price_weightage["hotel_id"],
            occupancy_start=competitive_price_weightage["occupancy_start"],
            occupancy_end=competitive_price_weightage["occupancy_end"],
            lower_competitive_price_multiplier=competitive_price_weightage["lower_competitive_price_multiplier"],
            higher_competitive_price_multiplier=competitive_price_weightage["higher_competitive_price_multiplier"],
            file_id=competitive_price_weightage.get("file_id"),
            is_deleted=competitive_price_weightage["is_deleted"],
        )
