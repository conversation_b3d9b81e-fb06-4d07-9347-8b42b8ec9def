from typing import List

from pydantic import BaseModel

from hawkeye.domain.entities.room_type_price import RoomTypePrice


class SkuPrice(BaseModel):
    sale_price: str
    sku_code: str
    room_type: str = None
    adults: int = 1


class PricePushEvent(BaseModel):
    property_code: str
    stay_date: str
    sku_prices: List[SkuPrice]
    persistent_override: bool

    def add_price_to_price_push_event_payload(self, room_type_price: RoomTypePrice):
        if room_type_price.room_type and room_type_price.sku_name:
            adults = int(room_type_price.sku_name.split('-')[-1])
        else:
            adults = 1

        price = SkuPrice(sale_price="{:.02f}".format(room_type_price.final_price), sku_code=room_type_price.sku_code,
                         room_type=room_type_price.room_type, adults=adults)
        self.sku_prices.append(price)
