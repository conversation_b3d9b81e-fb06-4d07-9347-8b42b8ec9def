from hawkeye.infrastructure.base_entity import EntityChangeTracker


class HotelSku(EntityChangeTracker):
    """
    Hotel SKU entity
    """

    def __init__(
        self,
        hotel_id,
        sku_code,
        sku_name,
        id=None,
        sku_category=None,
        rack_rate=None,
        description=None,
        extra_information=None,
        is_active=True,
        new=True,
        dirty=True,
    ):
        super().__init__(new, dirty)
        self.id = id
        self.hotel_id = hotel_id
        self.sku_code = sku_code
        self.sku_category = sku_category
        self.rack_rate = rack_rate
        self.description = description
        self.extra_information = extra_information
        self.sku_name = sku_name
        self.is_active = is_active

    def to_json(self):
        return dict(
            id=self.id,
            hotel_id=self.hotel_id,
            sku_code=self.sku_code,
            sku_category=self.sku_category,
            rack_rate=self.rack_rate,
            description=self.description,
            extra_information=self.extra_information,
            sku_name=self.sku_name,
            is_active=self.is_active,
        )

    @staticmethod
    def from_json(hotel_sku_data):
        return HotelSku(
            id=hotel_sku_data.get("id"),
            hotel_id=hotel_sku_data["hotel_id"],
            sku_code=hotel_sku_data["sku_code"],
            sku_category=hotel_sku_data.get("sku_category"),
            rack_rate=hotel_sku_data.get("rack_rate"),
            description=hotel_sku_data.get("description"),
            extra_information=hotel_sku_data.get("extra_information"),
            sku_name=hotel_sku_data["sku_name"],
            is_active=hotel_sku_data.get("is_active", True),
        )

    def update(self, sku):
        self.sku_name = sku.sku_name
        self.is_active = sku.is_active
        self.extra_information = sku.extra_information
        self.description = sku.description
        self.rack_rate = sku.rack_rate
        self.sku_category = sku.sku_category

    def mark_inactive(self):
        self.is_active = False
