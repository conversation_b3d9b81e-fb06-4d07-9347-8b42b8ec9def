from hawkeye.domain.entities.rule_file_upload import RuleFileUpload


class RuleFileUploadFactory:
    @staticmethod
    def create_rule_file_upload_config(rule_file_upload_dto):
        return RuleFileUpload(file_name=rule_file_upload_dto.file_name,
                              path=rule_file_upload_dto.path,
                              status=rule_file_upload_dto.status,
                              uploaded_by=rule_file_upload_dto.uploaded_by,
                              )
