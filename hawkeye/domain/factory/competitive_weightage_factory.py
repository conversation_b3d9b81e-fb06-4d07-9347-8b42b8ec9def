from hawkeye.domain.entities.competitive_price_weightage import CompetitivePriceWeightage


class CompetitivePriceWeightageFactory:

    @staticmethod
    def create_competitive_price_weightage(compset_weightage_dto):
        return CompetitivePriceWeightage(
            hotel_id=compset_weightage_dto.hotel_id,
            occupancy_start=compset_weightage_dto.occupancy_start,
            occupancy_end=compset_weightage_dto.occupancy_end,
            lower_competitive_price_multiplier=compset_weightage_dto.lower_competitive_price_multiplier,
            higher_competitive_price_multiplier=compset_weightage_dto.higher_competitive_price_multiplier,
            file_id=compset_weightage_dto.file_id,
        )

