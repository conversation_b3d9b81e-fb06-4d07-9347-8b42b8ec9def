from hawkeye.domain.entities.room_type_price import RoomTypePrice
from hawkeye.domain.entities.price_push_event import SkuPrice, PricePushEvent


class PricePushEventFactory(object):

    @staticmethod
    def create_price_push_event_payload(room_type_price: RoomTypePrice):
        if room_type_price.room_type and room_type_price.sku_name:
            adults = int(room_type_price.sku_name.split('-')[-1])
        else:
            adults = 1
        price = SkuPrice(sale_price="{:.02f}".format(room_type_price.final_price), sku_code=room_type_price.sku_code,
                         room_type=room_type_price.room_type, adults=adults)
        return PricePushEvent(property_code=room_type_price.hotel_id,
                              stay_date=room_type_price.target_date.isoformat(),
                              sku_prices=[price],
                              persistent_override=False)
