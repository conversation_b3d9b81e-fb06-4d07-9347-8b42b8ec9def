from hawkeye.domain.entities.base_room_type_price_rule import BaseRoomTypePriceRule


class BaseRoomTypePriceRuleFactory:
    @staticmethod
    def create_pricing_rule(pricing_rule_dto):
        return BaseRoomTypePriceRule(
            hotel_id=pricing_rule_dto.hotel_id,
            rule_name=pricing_rule_dto.rule_name,
            rule_type=pricing_rule_dto.rule_type,
            config=pricing_rule_dto.config,
            is_input_price=pricing_rule_dto.is_input_price,
            factor=pricing_rule_dto.factor,
            sum_factor=pricing_rule_dto.sum_factor,
            priority=pricing_rule_dto.priority,
            is_deleted=pricing_rule_dto.is_deleted,
            start_price=pricing_rule_dto.start_price,
            end_price=pricing_rule_dto.end_price,
            file_id=pricing_rule_dto.file_id,
        )
