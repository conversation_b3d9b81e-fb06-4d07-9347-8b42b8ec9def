from hawkeye.domain.entities.base_room_type_sku_price_rule import BaseRoomTypeSkuPriceRule


class BaseRoomTypeSkuPriceRuleFactory:
    @staticmethod
    def create_sku_pricing_rule(sku_pricing_rule_dto):
        return BaseRoomTypeSkuPriceRule(
            hotel_id=sku_pricing_rule_dto.hotel_id,
            sku_code=sku_pricing_rule_dto.sku_code,
            input_type=sku_pricing_rule_dto.input_type,
            rule_name=sku_pricing_rule_dto.rule_name,
            rule_type=sku_pricing_rule_dto.rule_type,
            config=sku_pricing_rule_dto.config,
            factor=sku_pricing_rule_dto.factor,
            sum_factor=sku_pricing_rule_dto.sum_factor,
            priority=sku_pricing_rule_dto.priority,
            is_deleted=sku_pricing_rule_dto.is_deleted,
            start_price=sku_pricing_rule_dto.start_price,
            end_price=sku_pricing_rule_dto.end_price,
            file_id=sku_pricing_rule_dto.file_id,
        )
