from hawkeye.domain.entities.compset_pricing_threshold import CompsetPricingThreshold


class CompsetPricingThresholdFactory:

    @staticmethod
    def create_compset_pricing_threshold(compset_pricing_threshold_dto):
        return CompsetPricingThreshold(
            hotel_id=compset_pricing_threshold_dto.hotel_id,
            stay_date=compset_pricing_threshold_dto.stay_date,
            increased_percentage_threshold=float(compset_pricing_threshold_dto.increased_percentage_threshold),
            decreased_percentage_threshold=float(compset_pricing_threshold_dto.decreased_percentage_threshold),
            normalized_competitive_price_avg=(
                float(compset_pricing_threshold_dto.normalized_competitive_price_avg)
                if compset_pricing_threshold_dto.normalized_competitive_price_avg else None
            ),
            file_id=compset_pricing_threshold_dto.file_id,
        )
