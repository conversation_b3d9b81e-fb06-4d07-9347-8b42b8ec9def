from hawkeye.constants.hawkeye_constant import HotelStatus
from hawkeye.domain.entities.hotel_config import HotelConfig
from hawkeye.domain.entities.hotel_room_type import HotelRoomType
from hawkeye.domain.entities.hotel_sku import HotelSku


class HotelFactory:
    @staticmethod
    def create_hotel_config(hotel_dto):
        return HotelConfig(
            hotel_id=hotel_dto.hotel_id,
            is_live=True if hotel_dto.status == HotelStatus.LIVE else False,
        )


class RoomTypeFactory:
    @staticmethod
    def create_room_type_config(hotel_room_type_dto):
        return HotelRoomType(
            hotel_id=hotel_room_type_dto.hotel_id,
            room_type_code=hotel_room_type_dto.room_type_code,
            room_type_name=hotel_room_type_dto.room_type_name,
            total_rooms=hotel_room_type_dto.total_rooms,
            max_adults=hotel_room_type_dto.max_adults,
        )

    @staticmethod
    def create_room_type_configs(hotel_room_type_dtos):
        return [
            RoomTypeFactory.create_room_type_config(hotel_room_type_dto)
            for hotel_room_type_dto in hotel_room_type_dtos
        ]


class HotelSkuFactory:
    @staticmethod
    def create_from_hotel_skus_dto(sku_dto):
        return HotelSku(
            is_active=sku_dto.is_active,
            sku_name=sku_dto.sku_name,
            sku_category=sku_dto.sku_category,
            sku_code=sku_dto.code,
            description=sku_dto.description,
            extra_information=sku_dto.extra_information,
            hotel_id=sku_dto.hotel_id,
            rack_rate=sku_dto.rack_rate,
        )

    @staticmethod
    def create_from_hotel_skus_dtos(sku_dtos):
        return [HotelSkuFactory.create_from_hotel_skus_dto(sku_dto) for sku_dto in sku_dtos]
