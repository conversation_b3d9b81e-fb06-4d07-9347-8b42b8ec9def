import logging
from typing import List, Optional

from hawkeye.domain.entities.base_room_type_sku_price_rule import BaseRoomTypeSkuPriceRule
from hawkeye.domain.entities.room_type_price import RoomTypePrice
from hawkeye.domain.entities.hotel_room_inventory import HotelRoomInventory
from hawkeye.domain.entities.hotel_room_type import HotelRoomType
from hawkeye.infrastructure.exception import NegativeFinalPriceError

logger = logging.getLogger(__name__)


class SkuPriceAggregate(object):
    def __init__(
        self,
        hotel_id,
        sku_entity,
        target_date,
        price_trigger_id,
        room_type_entity: HotelRoomType,
        room_type_entities: List[HotelRoomType],
        sku_price_rules,
        room_type_inventory_entities: Optional[List[HotelRoomInventory]] = None,
        occupancy: Optional[int] = None,
    ):
        self.hotel_id = hotel_id
        self.sku_entity = sku_entity
        self.target_date = target_date
        self.price_trigger_id = price_trigger_id
        self.room_type_entity = room_type_entity
        self.room_type_entities = room_type_entities
        self.sku_price_rules = sku_price_rules
        self.room_type_inventory_entities = room_type_inventory_entities if room_type_inventory_entities else []
        self.room_type_price_entity = None
        self.occupancy = occupancy
        self.total_rooms = sum([room_type_entity.total_rooms for room_type_entity in self.room_type_entities])
        self.sku_code = self.sku_entity.sku_code

    @property
    def room_type_code(self):
        return self.room_type_entity.room_type_code

    @property
    def room_type_name(self):
        return self.room_type_entity.room_type_name

    @property
    def sku_name(self):
        return self.sku_entity.sku_name

    @property
    def rack_rate(self):
        return self.sku_entity.rack_rate

    @property
    def room_sku_name(self):
        return f'{self.room_type_name}-{self.occupancy}'

    @property
    def hotel_inventory_availability_count(self):
        availability_count = 0
        for hotel_room_inventory in self.room_type_inventory_entities:
            if hotel_room_inventory.availability_count > 0:
                availability_count += hotel_room_inventory.availability_count
        return availability_count

    @property
    def hotel_inventory_out_of_order(self):
        return sum([hotel_room_inventory.out_of_order for hotel_room_inventory in self.room_type_inventory_entities])

    @property
    def hotel_occupancy(self):
        total_rooms = self.total_rooms - self.hotel_inventory_out_of_order
        occupied_rooms = total_rooms - self.hotel_inventory_availability_count

        if occupied_rooms < 0 or total_rooms < 0:
            logger.info(f"Inconsistent inventories for hotel {self.hotel_id} and date {self.target_date}")
            return 0

        if total_rooms == 0:
            return 0

        return (occupied_rooms / total_rooms) * 100

    def finalize_price(self, room_type_price_entity: RoomTypePrice):
        if room_type_price_entity.final_price < 0:
            raise NegativeFinalPriceError(room_type_price_entity.sku_name)
        room_type_price_entity.final_price = float(f"{room_type_price_entity.final_price:.2f}")
        room_type_price_entity.price_trigger_id = self.price_trigger_id
        room_type_price_entity.populate_is_skipped()

    def set_room_type_price(self, room_type_price_entity: RoomTypePrice):
        self.room_type_price_entity = room_type_price_entity
