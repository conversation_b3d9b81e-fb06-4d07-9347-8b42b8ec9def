from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor

from treebo_commons.utils import dateutils

from hawkeye.application.services.dtos.inventory_dtos import RoomTypeInventoryDto
from hawkeye.domain.factory.hotel_inventory_factory import HotelInventoryFactory
from hawkeye.domain.price_calculation.aggregates.price_aggregate import PriceAggregate, logger
from hawkeye.domain.price_calculation.aggregates.sku_price_aggregate import SkuPriceAggregate
from hawkeye.utils.collectionutils import chunks


class PriceFactory(object):

    @classmethod
    def _get_date_wise_inventories(cls, hotel_room_inventories):
        date_wise_inventories = defaultdict(list)
        for inventory in hotel_room_inventories:
            date_wise_inventories[inventory.date].append(inventory)
        return date_wise_inventories

    @classmethod
    def create_hotel_inventory(cls, hotel_id, target_date, room_type_entity):
        room_type_inventory_dto = RoomTypeInventoryDto.create_from_inventory_data(
            {
                "hotel_id": hotel_id,
                "room_type_id": room_type_entity.room_type_code,
                "date": target_date.isoformat(),
                "actual_count": room_type_entity.total_rooms,
            },
            should_increment_dnr=None,
            event_type=None,
        )
        return HotelInventoryFactory.create_hotel_inventory(
            room_type_inventory_dto
        )

    @classmethod
    def prepare_room_type_inventories(cls, date_wise_inventories, room_type_entities, hotel_id, target_dates):
        room_type_inventories = {}
        for target_date in target_dates:
            target_date = target_date.date()
            inventories = date_wise_inventories.get(target_date, [])
            inventory_room_types = {inventory.room_type for inventory in inventories}

            for rt_entity in room_type_entities:
                if rt_entity.room_type_code not in inventory_room_types:
                    inventories.append(
                        cls.create_hotel_inventory(hotel_id, target_date, rt_entity)
                    )

            room_type_inventories[target_date] = inventories
        return room_type_inventories

    @classmethod
    def create_base_room_type_prices(
        cls,
        hotel_id,
        start_date,
        end_date,
        price_trigger_id,
        base_room_type_price_rules,
        room_type_entities,
        hotel_room_inventories,
        incremental_price_rules,
        competitive_price_weightages,
        compset_pricing_thresholds,
    ):
        date_wise_inventories = cls._get_date_wise_inventories(hotel_room_inventories)
        if isinstance(base_room_type_price_rules, list):
            price_rules = defaultdict(list)
            for rule in base_room_type_price_rules:
                price_rules[rule.rule_type].append(rule)
        else:
            price_rules = base_room_type_price_rules
        price_aggregates = list()
        for target_datetime in dateutils.date_range(start_date, end_date, end_inclusive=True):
            target_date = target_datetime.date()
            room_type_inventories = date_wise_inventories.get(target_date, [])
            inventory_room_types = [inventory.room_type for inventory in room_type_inventories]
            for room_type_entity in room_type_entities:
                if room_type_entity.room_type_code not in inventory_room_types:
                    room_type_inventories.append(cls.create_hotel_inventory(hotel_id, target_date, room_type_entity))

            price_aggregate = PriceAggregate(
                hotel_id,
                target_date,
                price_trigger_id,
                price_rules,
                room_type_entities,
                room_type_inventories,
                incremental_price_rules,
                competitive_price_weightages,
                compset_pricing_thresholds,
            )
            price_aggregates.append(price_aggregate)

        return price_aggregates

    @classmethod
    def create_sku_price_aggregates(
            cls,
            hotel_id,
            start_date,
            end_date,
            price_trigger_id,
            sku_price_rules,
            room_type_entities,
            sku_name_wise_sku_entities,
            hotel_room_inventories,
    ):
        batch_size = 1000
        extra_adult_sku_price_aggregates = []
        room_type_sku_price_aggregates = []

        # Pre-process all the data
        date_wise_inventories = cls._get_date_wise_inventories(hotel_room_inventories)
        room_type_name_wise_room_type_entity = {
            room_type_entity.room_type_name.upper(): room_type_entity
            for room_type_entity in room_type_entities
        }

        target_dates = list(dateutils.date_range(start_date, end_date, end_inclusive=True))
        date_wise_room_type_inventories = cls.prepare_room_type_inventories(
            date_wise_inventories, room_type_entities, hotel_id, target_dates
        )

        def generate_sku_price_aggregates(sku_name, room_type_entities_to_process, is_extra_adult=False, dates=None):
            sku_price_aggregates = []
            sku_configs = []
            for rt_entity in room_type_entities_to_process:
                max_adults = rt_entity.max_adults if not is_extra_adult else 1
                for occupancy in range(1, max_adults + 1):
                    rt_sku_name = (
                        sku_name.upper() if is_extra_adult
                        else f"{sku_name} {rt_entity.room_type_name}-{occupancy}-0".upper()
                    )
                    rt_sku_entity = sku_name_wise_sku_entities.get(rt_sku_name)
                    if not rt_sku_entity:
                        logger.info(f"Sku Mapping not found for {rt_sku_name} for hotel {hotel_id}")
                        continue
                    sku_configs.append((rt_entity, rt_sku_entity, occupancy))

            for target_date in dates:
                target_date = target_date.date()
                room_type_inventories = date_wise_room_type_inventories[target_date]

                date_aggregates = [
                    SkuPriceAggregate(
                        hotel_id,
                        sku_config[1],  # rt_sku_entity
                        target_date,
                        price_trigger_id,
                        sku_config[0],  # rt_entity
                        room_type_entities,
                        sku_wise_price_rules[sku_name],
                        room_type_inventories,
                        occupancy=sku_config[2],
                    )
                    for sku_config in sku_configs
                ]
                sku_price_aggregates.extend(date_aggregates)
                
            return sku_price_aggregates

        sku_wise_price_rules = defaultdict(lambda: defaultdict(list))
        extra_adult_sku_names, base_room_type_sku_names = set(), set()
        for sku_price_rule in sku_price_rules:
            sku_wise_price_rules[sku_price_rule.sku_code][sku_price_rule.rule_type].append(sku_price_rule)
            if sku_price_rule.priority == 1:
                extra_adult_sku_names.add(sku_price_rule.sku_code)
            else:
                base_room_type_sku_names.add(sku_price_rule.sku_code)

        with ThreadPoolExecutor() as executor:
            for chunk in chunks(target_dates, batch_size):
                futures = []
                for sku_name in extra_adult_sku_names:
                    if room_type_name_wise_room_type_entity.get(sku_name.upper().split("-")[0]):
                        futures.append(executor.submit(
                            generate_sku_price_aggregates,
                            sku_name,
                            [room_type_name_wise_room_type_entity.get(sku_name.upper().split("-")[0])],
                            True,
                            chunk
                        ))

                for future in futures:
                    extra_adult_sku_price_aggregates.extend(future.result())

                futures = []
                for sku_name in base_room_type_sku_names:
                    futures.append(executor.submit(
                        generate_sku_price_aggregates,
                        sku_name,
                        room_type_entities,
                        False,
                        chunk
                    ))

                for future in futures:
                    room_type_sku_price_aggregates.extend(future.result())

        return extra_adult_sku_price_aggregates, room_type_sku_price_aggregates
