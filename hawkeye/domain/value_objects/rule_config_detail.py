import abc
from datetime import datetime

from treebo_commons.utils import dateutils


class RuleConfigDetail(abc.ABC):
    @abc.abstractmethod
    def to_json(self):
        pass


class OccupancyRangeRuleConfigDetail(RuleConfigDetail):
    def __init__(self, occupancy_start: int, occupancy_end: int):
        self.occupancy_start = occupancy_start
        self.occupancy_end = occupancy_end

    def to_json(self):
        return {
            "occupancy_start": self.occupancy_start,
            "occupancy_end": self.occupancy_end,
        }

    @classmethod
    def from_json(cls, rule_detail_json):
        return cls(
            occupancy_start=rule_detail_json["occupancy_start"],
            occupancy_end=rule_detail_json["occupancy_end"],
        )

    def __eq__(self, other):
        return (
                isinstance(other, OccupancyRangeRuleConfigDetail)
                and self.occupancy_start == other.occupancy_start
                and self.occupancy_end == other.occupancy_end
        )


class ABWRuleConfigDetail(RuleConfigDetail):
    def __init__(self, abw_start: int, abw_end: int):
        self.abw_start = abw_start
        self.abw_end = abw_end

    def to_json(self):
        return {"abw_start": self.abw_start, "abw_end": self.abw_end}

    @classmethod
    def from_json(cls, rule_detail_json):
        return cls(
            abw_start=rule_detail_json["abw_start"], abw_end=rule_detail_json["abw_end"]
        )

    def __eq__(self, other):
        return (
                isinstance(other, ABWRuleConfigDetail)
                and self.abw_start == other.abw_start
                and self.abw_end == other.abw_end
        )


class TargetDateRuleConfigDetail(RuleConfigDetail):
    def __init__(self, target_date: datetime.date):
        self.target_date = target_date

    def to_json(self):
        return {"target_date": dateutils.date_to_ymd_str(self.target_date)}

    @classmethod
    def from_json(cls, rule_detail_json):
        return cls(target_date=dateutils.ymd_str_to_date(rule_detail_json["target_date"]))

    def __eq__(self, other):
        return (
                isinstance(other, TargetDateRuleConfigDetail)
                and self.target_date == other.target_date
        )


class SameDayOccupancyRuleConfigDetail(OccupancyRangeRuleConfigDetail):
    def __init__(self, occupancy_start: int, occupancy_end: int):
        super(SameDayOccupancyRuleConfigDetail, self).__init__(
            occupancy_start, occupancy_end
        )


class ExtraAdultOccupancyRuleConfigDetail(RuleConfigDetail):
    def __init__(self, extra_adult_price, subsequent_adult_price):
        self.extra_adult_price = extra_adult_price
        self.subsequent_adult_price = subsequent_adult_price

    def to_json(self):
        return {"extra_adult_price": self.extra_adult_price, "subsequent_adult_price": self.subsequent_adult_price}

    @classmethod
    def from_json(cls, rule_detail_json):
        return cls(
            extra_adult_price=rule_detail_json["extra_adult_price"],
            subsequent_adult_price=rule_detail_json["subsequent_adult_price"],
        )

    def __eq__(self, other):
        return (
            isinstance(other, ExtraAdultOccupancyRuleConfigDetail)
            and self.extra_adult_price == other.extra_adult_price
            and self.subsequent_adult_price == other.subsequent_adult_price
        )
