import json

from hawkeye.constants.hawkeye_constant import RuleType
from hawkeye.domain.value_objects.rule_config_detail import (
    ABWRuleConfigDetail,
    OccupancyRangeRuleConfigDetail,
    RuleConfigDetail,
    SameDayOccupancyRuleConfigDetail,
    TargetDateRuleConfigDetail, ExtraAdultOccupancyRuleConfigDetail,
)


class RuleDefinition:
    RULE_CONFIG_DETAIL_CLASS_MAP = {
        RuleType.PERCENTAGE_OCCUPANCY: OccupancyRangeRuleConfigDetail,
        RuleType.ADVANCE_BOOKING_WINDOW: ABWRuleConfigDetail,
        RuleType.TARGET_DATE: TargetDateRuleConfigDetail,
        RuleType.PERCENTAGE_OCCUPANCY_EVENING: SameDayOccupancyRuleConfigDetail,
        RuleType.EXTRA_ADULT_OCCUPANCY: ExtraAdultOccupancyRuleConfigDetail,
    }

    def __init__(
            self,
            rule_type: RuleType,
            rule_config_detail: RuleConfigDetail,
            is_input_price: bool = False,
            input_price: float = 0.0,
            multiplier: float = 1.0,
            sum_factor: float = 0.0,
    ):
        self._rule_type = rule_type
        self._rule_config_detail = rule_config_detail
        self._is_input_price = is_input_price
        self._input_price = input_price
        self._multiplier = multiplier
        self._sum_factor = sum_factor

    @staticmethod
    def from_json(rule_definition_json):
        rule_type = RuleType(rule_definition_json["rule_type"])
        rule_config_detail_class = RuleDefinition.RULE_CONFIG_DETAIL_CLASS_MAP.get(
            rule_type
        )

        return RuleDefinition(
            rule_type=rule_type,
            rule_config_detail=rule_config_detail_class.from_json(
                rule_detail_json=rule_definition_json["rule_config_detail"]
            )
            if rule_config_detail_class
            else None,
            is_input_price=rule_definition_json["is_input_price"],
            input_price=rule_definition_json["input_price"],
            multiplier=rule_definition_json["multiplier"],
            sum_factor=rule_definition_json["sum_factor"],
        )

    def to_json(self):
        return {
            "rule_type": self._rule_type.value,
            "rule_config_detail": self._rule_config_detail.to_json()
            if self._rule_config_detail
            else None,
            "is_input_price": self._is_input_price,
            "input_price": float(self._input_price) if self._input_price is not None else self._input_price,
            "multiplier": float(self._multiplier) if self._multiplier is not None  else self._multiplier,
            "sum_factor": float(self._sum_factor) if self._sum_factor is not None else self._sum_factor,
        }

    def __eq__(self, other):
        return (
                isinstance(other, RuleDefinition)
                and self._rule_type == other._rule_type
                and self._rule_config_detail == other._rule_config_detail
                and self._is_input_price == other._is_input_price
                and self._input_price == other._input_price
                and self._multiplier == other._multiplier
                and self._sum_factor == other._sum_factor
        )


class RuleDefinitions:
    def __init__(self, rule_definitions):
        self.rule_definitions = rule_definitions

    @staticmethod
    def from_json(rule_definitions_json):
        return [
            RuleDefinition.from_json(rule_definition_json)
            for rule_definition_json in rule_definitions_json
            if rule_definition_json
        ]

    def to_json(self):
        return json.dumps(
            [
                rule_definition.to_json()
                for rule_definition in self.rule_definitions
                if rule_definition
            ]
        )
