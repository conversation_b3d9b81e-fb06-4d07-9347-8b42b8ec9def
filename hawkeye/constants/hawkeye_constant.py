import os

from hawkeye.constants.base_enum import BaseEnum


class RuleFileUploadStatus(BaseEnum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    PARTIAL_SUCCESS = "partial_success"
    SUCCESS = "success"
    FORMAT_ERROR = "format_error"
    FAILED = "failed"


class RuleType(BaseEnum):
    PERCENTAGE_OCCUPANCY = "percentageOccupancy"
    ADVANCE_BOOKING_WINDOW = "advanceBookingWindow"
    TARGET_DATE = "targetDate"
    PERCENTAGE_OCCUPANCY_EVENING = "percentageOccupancyEvening"
    PRICE_BOUNDARIES = "priceBoundaries"
    DEFAULT_RACK_RATE = "defaultRackRate"
    EXTRA_ADULT_OCCUPANCY = "extraAdultOccupancy"
    COMPETITIVE_PRICE = "competitivePrice"


class CatalogEvents(BaseEnum):
    PROPERTY = "PROPERTY"
    ROOM_TYPE_CONFIG = "ROOM_TYPE_CONFIG"
    ROOM_RACK_RATE = "ROOM_RACK_RATE"

    class OperationTypes(BaseEnum):
        CREATE = "CREATE"
        UPDATE = "UPDATE"
        DELETE = "DELETE"


class HotelStatus(BaseEnum):
    """
    Enum for Hotel Status
    """

    LIVE = "LIVE"
    NEAR_CONFIRMED = "NEAR_CONFIRMED"
    NOT_SIGNING = "NOT_SIGNING"
    SIGNED = "SIGNED"
    DROPPED_POST_SIGNING = "DROPPED_POST_SIGNING"
    CHURNED = "CHURNED"


class CrsEventType(BaseEnum):
    INVENTORY_ROOM_TYPE_UPDATED = "inventory.room_type.updated"
    DNR_SET = "inventory.dnr.set"
    DNR_UPDATED = "inventory.dnr.updated"
    DNR_REMOVED = "inventory.dnr.removed"
    DNR_RELEASED = "inventory.dnr.released"
    HOUSEKEEPING_RECORD_UPDATED = "housekeeping_record.updated"
    INVENTORY_DNR = "inventory.dnr"


class CrsEventEntity(BaseEnum):
    ROOM_TYPE_INVENTORY = "room_type_inventory"
    DNR = "dnr"


class PricePushEventStatus:
    SUCCESS = "success"
    FAILED = "failed"


class PriceTriggerSource:
    AUTOMATIC = "AUTOMATIC"
    MANUAL = "MANUAL"
    INVENTORY = "INVENTORY"
    PRICE_ALERT_APPROVAL = "PRICE_ALERT_APPROVAL"


class SkuNameComponent:
    EXTRA_ADULT = "EXTRA-ADULT"
    SUBSEQUENT_EXTRA_ADULT = "SUBSEQUENT-EXTRA-ADULT"
    ECI = "ECI"
    LCO = "LCO"


class SkuNames(BaseEnum):
    ECI_B6 = "ECI-B6"
    ECI_BT69 = "ECI-BT69"
    ECI_A9 = "ECI-A9"
    LCO_BT122 = "LCO-BT122"
    LCO_BT24 = "LCO-BT24"
    LCO_A4 = "LCO-A4"
    MAPLE_EXTRA_ADULT = "MAPLE-EXTRA-ADULT"
    MAPLE_SUBSEQUENT_EXTRA_ADULT = "MAPLE-SUBSEQUENT-EXTRA-ADULT"
    OAK_EXTRA_ADULT = "OAK-EXTRA-ADULT"
    OAK_SUBSEQUENT_EXTRA_ADULT = "OAK-SUBSEQUENT-EXTRA-ADULT"
    ACACIA_EXTRA_ADULT = "ACACIA-EXTRA-ADULT"
    ACACIA_SUBSEQUENT_EXTRA_ADULT = "ACACIA-SUBSEQUENT-EXTRA-ADULT"
    MAHOGANY_EXTRA_ADULT = "MAHOGANY-EXTRA-ADULT"
    MAHOGANY_SUBSEQUENT_EXTRA_ADULT = "MAHOGANY-SUBSEQUENT-EXTRA-ADULT"


class InputTypes(BaseEnum):
    BASE_ROOM_RATE = "BASE ROOM RATE"
    MANUAL = "MANUAL"


class CommunicationTypeIdentifier(BaseEnum):
    PRICE_APPROVAL_ALERT = "price_approval_alert"


class RateInfoMealPlanCode(BaseEnum):
    CP = 6


DNR_ACTIVE_STATUS = "active"
INVENTORY_SYNC_DAYS = 365 * 2
BASE_ROOM_PRICE_CACHE_EXPIRY = 1
SKU_CODE_CACHE_EXPIRY = 30 * 24 * 60 * 60
HOTEL_ROOM_TYPE_CONFIG_CACHE_EXPIRY = 30 * 24 * 60 * 60  # 30 days

PRICE_PUSH_OVERRIDE_SOURCE = "HAWKEYE"
PRICE_PUSH_COMBINED_DATES = 10  # Number of dates
IGNORE_PRICE_DIFFERENCE = 5.0  # In currency unit i.e. 5 rupees
IGNORE_COMPETITIVE_PRICE_DIFFERENCE = 10.0  # In currency unit i.e. 10 rupees
SERVICE_SHORT_NAME = os.environ.get("APP_NAME", default="hawkeye")
SKU_STAY_CATEGORY_CODE = "stay"
BASE_ROOM_CONFIG = "1-0"

PERCENTAGE_OCCUPANCY_EVENING_TIME_LIMIT = "18:00:00"
PERCENTAGE_OCCUPANCY_EVENING_ABW_LIMIT = 1  # In days
DEFAULT_SUM_FACTOR = 0.0

AUTHENTICATION_SECRET_KEY = "ZWFhNzdmMWUzNTFmMTFlZGI5M2M5MmU0ZGFmZDQ3NGM="


PRICE_APPROVAL_ALERT_SUBJECT = "Action Required: Price change alert {}, {} for {}"
RATE_PING_DB_SECRET_NAME = "rate_ping/postgres"
CATALOG_HOTEL_DETAILS_CACHE_EXPIRY = 7 * 24 * 60 * 60  # 7 days

GUARDRAIL_MAX = 15000
GUARDRAIL_MIN = 999
