import logging

from fastapi import APIRouter

from hawkeye.api.room_type.schemas import RoomTypesResponse
from hawkeye.application.services.catalog_service import CatalogService
from object_registry import locate_instance

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/non-base-room-types/{hotel_id}", response_model=RoomTypesResponse)
async def get_non_base_room_types_for_hotel(hotel_id: str):
    """
    get non-base room types for hotel id
    """
    catalog_service = locate_instance(CatalogService)
    response = {"room_types": catalog_service.get_non_base_room_types_for_hotel(hotel_id)}
    return RoomTypesResponse(**response)
