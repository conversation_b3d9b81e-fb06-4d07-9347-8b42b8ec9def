from decimal import Decimal, InvalidOperation
from typing import Optional
from datetime import datetime, timedelta

from pydantic import BaseModel, root_validator, constr

from hawkeye.constants.hawkeye_constant import RuleType, SkuNames, SkuNameComponent, InputTypes


class RuleFileCsvRequest(BaseModel):
    hotel_id: str
    rule_type: str
    start_range: Optional[str]
    end_range: Optional[str]
    multiplier: Optional[str]
    addition: Optional[str]
    start_price: Optional[str]
    end_price: Optional[str]
    sku: Optional[constr(strip_whitespace=True, min_length=1)]
    input_type: Optional[str]

    class Config:
        anystr_strip_whitespace = True  # removing leading and trailing spaces

    @root_validator(pre=False)
    def validate_data(cls, values):
        cls.validate_rule_type(values)
        cls.validate_mandatory_values(values)
        cls.validate_numeric_values(values)
        cls.validate_range_values(cls, values)
        cls.validate_price_range(values)
        return values

    @staticmethod
    def validate_rule_type(values):
        if values["rule_type"] not in RuleType.all():
            raise ValueError(f"{values['rule_type']} is not a valid Rule Type")

    @staticmethod
    def validate_mandatory_values(values):
        if values["rule_type"] != RuleType.PRICE_BOUNDARIES.value and not (
                values.get('start_range') and values.get('end_range')):
            raise ValueError(
                f"For rule {values['rule_type']} and hotel {values['hotel_id']}, complete range is not provided")

        if not values.get('sku') and values["rule_type"] == RuleType.PERCENTAGE_OCCUPANCY.value and not (
                values.get('start_price') and values.get('end_price')):
            raise ValueError(f"For hotel {values['hotel_id']}, percentage occupancy price range is mandatory")

        if values["rule_type"] == RuleType.PRICE_BOUNDARIES.value and not (
            values.get('start_price') and values.get('end_price')
        ):
            raise ValueError(
                f"For hotel {values['hotel_id']} priceBoundaries rule both start_price and end_price are required")

        if values.get('sku'):
            if not values.get('input_type'):
                raise ValueError(f"For hotel {values['hotel_id']}, input_type is mandatory")
            sku = values['sku'].upper()
            input_type = values['input_type'].upper()
            if sku not in SkuNames.all():
                raise ValueError(f"For hotel {values['hotel_id']}, sku name is invalid")
            if SkuNameComponent.EXTRA_ADULT in sku and input_type != InputTypes.MANUAL.value:
                raise ValueError(f"For hotel {values['hotel_id']} and {sku} input_type should only be MANUAL")
            if sku[0:3] in [SkuNameComponent.LCO, SkuNameComponent.ECI] and input_type != InputTypes.BASE_ROOM_RATE.value:
                raise ValueError(f"For hotel {values['hotel_id']} and {sku} input_type should only be BASE ROOM RATE")

    def validate_range_values(self, values):
        if values.get('start_range') and values.get('end_range'):
            if values['rule_type'] == RuleType.TARGET_DATE.value:
                self._validate_target_date_rule_type(values)
            else:
                start_value = int(values['start_range'])
                end_value = int(values['end_range'])
                if start_value < 0 or end_value < 0:
                    raise ValueError(f"start_range or end_range should always be non-negative")
                if values['rule_type'] == RuleType.ADVANCE_BOOKING_WINDOW.value:
                    if not (0 <= start_value <= 365):
                        raise ValueError(f"start_range {values['start_range']} is out of valid range (0 to 365)")
                    if not (0 <= end_value <= 365):
                        raise ValueError(f"end_range {values['end_range']} is out of valid range (0 to 365)")
                if values['rule_type'] in [RuleType.PERCENTAGE_OCCUPANCY.value, RuleType.PERCENTAGE_OCCUPANCY_EVENING.value]:
                    if not (0 <= start_value <= 100):
                        raise ValueError(f"start_range {values['start_range']} is out of valid range (0 to 100)")
                    if not (0 <= end_value <= 100):
                        raise ValueError(f"end_range {values['end_range']} is out of valid range (0 to 100)")
                if start_value > end_value:
                    raise ValueError(
                        f"start_range {values['start_range']} cannot be greater than end_range {values['end_range']}")

    @staticmethod
    def validate_price_range(values):
        start_price = Decimal(values.get('start_price') or '0')
        end_price = Decimal(values.get('end_price') or '0')
        if (not start_price) != (not end_price):
            raise ValueError(
                f"For rule {values['rule_type']} and hotel {values['hotel_id']}, "
                f"either both start_price and end_price must be provided and > 0, or neither should be provided"
            )
        if start_price and end_price and Decimal(start_price) > Decimal(end_price):
            raise ValueError(
                f"For rule {values['rule_type']} and hotel {values['hotel_id']}, "
                f"start_price {values.get('start_price', 'N/A')} cannot be greater than end_price {values.get('end_price', 'N/A')}"
            )

    @staticmethod
    def _validate_target_date_rule_type(values):
        try:
            start_value = datetime.strptime(values['start_range'], '%Y%m%d').date()
            end_value = datetime.strptime(values['end_range'], '%Y%m%d').date()
        except ValueError as ve:
            raise ValueError(
                f"For rule {values['rule_type']} and hotel {values['hotel_id']}, "
                f"target dates are invalid(start_range={values['start_range']}, end_range={values['end_range']}), Error: {str(ve)}")
        current_date = datetime.now().date()
        if start_value > end_value:
            raise ValueError(f"Start date cannot be greater than end date")
        if (start_value < current_date) or (end_value < current_date):
            raise ValueError(f"Past dated target dates not allowed")
        max_value = current_date + timedelta(days=365)
        if (start_value > max_value) or (end_value > max_value):
            raise ValueError(f"target date cannot be greater than {max_value.strftime('%Y-%m-%d')}")
        if not (0 <= (end_value - start_value).days <= 365):
            raise ValueError(f"Difference in target date range should be between (0 to 365)")

    @staticmethod
    def validate_numeric_values(values):
        numeric_fields = ['multiplier', 'addition', 'start_price', 'end_price']
        for field in numeric_fields:
            if values.get(field):
                try:
                    value = Decimal(values[field])
                except InvalidOperation:
                    raise ValueError(f"{field} must be a valid numeric value")
                if field == 'multiplier' and value < 0:
                    raise ValueError(f"{field} must be non negative")

    @root_validator(pre=False)
    def convert_data(cls, values):
        values["multiplier"] = Decimal(values.get("multiplier") or '1.0')
        values["addition"] = Decimal(values.get("addition") or '0.0')
        values["start_price"] = Decimal(values.get("start_price") or '0.0')
        values["end_price"] = Decimal(values.get("end_price") or '0.0')
        return values
