{% extends "sqladmin/layout.html" %}
{% block head %}
    <title>Rule file Upload Tool</title>
    <meta name="description" content="File Upload">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.3.1/dist/jquery.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/fomantic-ui@2.7.6/dist/semantic.min.css">
    <script src="https://cdn.jsdelivr.net/npm/fomantic-ui@2.7.6/dist/semantic.min.js"></script>
    <style>#republish_price_confirmation {text-align:center;} </style>

{% endblock %}

{% block content %}
    <div class="ui container">
        <h1 class="ui center aligned header" style="margin-top: 32px;">
            Re-publish Price
        </h1>
        <form class="ui form " method=POST id="republish_price_form" >
            <label for="hotel_id">Hotel ID:</label><br>
            <input type="text" id="hotel_id" name="hotel_id"><br>
            <label for="start_date">Start Date (YYYY-MM-DD):</label><br>
            <input type="text" id="start_date" name="start_date"><br>
            <label for="end_date">End Date (YYYY-MM-DD):</label><br>
            <input type="text" id="end_date" name="end_date"><br>

        </form>
        <dialog id="republish_price_confirmation">
            <p> Are you sure you want to re-publish prices? </p>
            <div>
                <button class="ui grey basic button " type="submit" style="margin-top: 50px;" id="pricepushbtnfinal">Proceed</button>
                <button class="ui grey basic button " type="submit" style="margin-top: 50px;margin-left:20px" id="cancelbtnfinal">Cancel</button>
            </div>
        </dialog>
        <button class="ui grey basic button " type="submit" style="margin-top: 20px;margin-left: 47%" id="pricepushbtn">Re-publish prices</button>
        <script>
            document.body.onload = initPage

            function initPage(){
                $("#pricepushbtn").click(displayConfirmationDialog);
                $("#pricepushbtnfinal").click(submitform);
                $("#cancelbtnfinal").click(closeConfirmationDialog);
            }
            function displayConfirmationDialog(){
                $("#republish_price_confirmation").show()
            }
            function closeConfirmationDialog(){
                $("#republish_price_confirmation").hide()
            }
            function submitform(){
                const form = document.forms.namedItem("republish_price_form");
                const formData = new FormData(form);
                var object = {};
                formData.forEach(function(value, key){
                    object[key] = value;
                });
                var json = JSON.stringify(object);
                $.ajax({type: "POST",
                     headers:{ 'Content-Type': 'application/json' },
                     url: "",
                     data: json,
                     success: function(response){
                        error = response['error']
                        if(!$.isEmptyObject(error)){
                             alert(response['response']+ error)
                        }else{
                             alert(response['response'])
                        }
                    },
                    error: function(response){
                            alert("Something Went Wrong \n "+ response.responseJSON["detail"]);
                    },
                })
                $("#republish_price_confirmation").hide()
            }
        </script>
    </div>
{% endblock %}
