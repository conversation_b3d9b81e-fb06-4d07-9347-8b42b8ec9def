{% extends "sqladmin/layout.html" %}
{% block content %}
<div class="col-12">l
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">New {{ model_view.name }}</h3>
    </div>
    <div class="card-body border-bottom py-3">
      <form action="{{ request.url }}" method="POST">
        <fieldset class="form-fieldset">
          <div class="mb-3 form-group row">
            <label class="form-label col-sm-2 col-form-label">Sum Factor</label>
            <div class="col-sm-10">
                <input class="form-control" id="sum_factor" name="sum_factor" step="any" type="number" value="0.0">
            </div>
          </div>
          <div class="mb-3 form-group row">
            <label class="form-label col-sm-2 col-form-label">Hotel Id</label>
            <div class="col-sm-10">
                <input class="form-control" id="hotel_id" name="hotel_id" step="any" type="text" value>
            </div>
          </div>
            <div class="mb-3 form-group row">
            <label class="form-label col-sm-2 col-form-label">Room Type</label>
            <div class="col-sm-10">
                <select name="room_type" id="room_types_list" style="width: 100%;height: 37px"></select>
            </div>
          </div>
            <div class="mb-3 form-group row">
            <label class="form-label col-sm-2 col-form-label">Factor</label>
            <div class="col-sm-10">
                <input class="form-control" id="factor" name="factor" step="any" type="number" value="1" disabled>
            </div>
          </div>
        </fieldset>
        <div class="d-flex align-items-right">
          <div class="row">
            <div class="col">
              <a href="{{ url_for('admin:list', identity=model_view.identity) }}" class="btn">
                Cancel
              </a>
            </div>
            <div class="col">
              <input type="submit" value="Save" class="btn btn-primary">
            </div>
          </div>
        </div>
      </form>
      <script>
          const hotelId = document.getElementById("hotel_id");
          const roomTypesList = document.getElementById("room_types_list");

          hotelId.addEventListener('blur',(event)=>
          {
              if ((hotelId.value != '') && (hotelId.value.length >=7))
               {
                  fetch(window.location.origin+'/hawkeye/v1/non-base-room-types/'+event.target.value)
                   .then((response) => response.json())
                   .then((data) =>
                   {
                     roomTypesList.innerHTML = ''
                     for(let room_type in data.room_types)
                     {
                       const option = document.createElement("option");
                       option.setAttribute("value",data.room_types[room_type]);
                       option.innerHTML  = data.room_types[room_type];
                       roomTypesList.appendChild(option);
                     }
                   });
               }
          });
      </script>
    </div>
  </div>

</div>
{% endblock %}
