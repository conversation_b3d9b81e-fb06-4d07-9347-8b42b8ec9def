{% extends "sqladmin/layout.html" %}
{% block head %}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin File Upload</title>

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 600px;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        #toast {
            display: none;
            min-width: 250px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 5px;
            padding: 16px;
            position: fixed;
            left: 50%;
            bottom: 30px;
            transform: translateX(-50%);
            z-index: 1;
        }
        .uploaded-file {
            margin-top: 15px;
            font-weight: bold;
            color: #000; /* Neutral color */
        }
        .hidden {
            display: none;
        }
        .error-message {
            color: red;
            font-weight: bold;
            margin-top: 10px;
        }
    </style>
{% endblock %}

{% block content %}

    <div class="container mt-5">
        <h3 class="text-center">Admin File Upload</h3>

        <form id="uploadForm" enctype="multipart/form-data" class="mt-4">
            <div class="mb-3">
                <label for="fileType" class="form-label">Select File Type:</label>
                <select name="file_type" id="fileType" class="form-select" required>
                    <option value="competitive_price_weightage">Compset Weightage</option>
                    <option value="compset_pricing_threshold">Price Threshold</option>
                    <option value="rule_file">Rule File</option>
                    <option value="enable_hotel_rate_ping">Enable Hotel Competitive Pricing</option>
                    <option value="competition_hotel_room_mappings">Competition Hotel Room Mappings</option>
                    <option value="competitive_hotel_mapping">Competitive Hotel Mappings</option>

                </select>
            </div>

            <div class="mb-3">
                <label for="fileInput" class="form-label">Choose File:</label>
                <input type="file" name="file" id="fileInput" class="form-control" required>
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">Upload</button>
                <button type="button" class="btn btn-secondary" id="clearFileBtn">Clear File</button>
            </div>
        </form>

        <div id="uploadedFileName" class="uploaded-file hidden mt-3"></div>

        <div id="failedRowsMessage" class="error-message hidden">🔴 Some rows failed. Check the errors below.</div>

        <div id="toast"></div>

        <div class="mt-4">
            <table id="errorTable" class="table table-bordered hidden">
                <thead class="table-danger">
                    <tr>
                        <th>Row</th>
                        <th>Error</th>
                    </tr>
                </thead>
                <tbody id="errorTableBody"></tbody>
            </table>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.getElementById("clearFileBtn").addEventListener("click", function () {
            document.getElementById("fileInput").value = "";
            document.getElementById("uploadedFileName").classList.add("hidden");
            clearErrorTable();
        });

        document.getElementById("uploadForm").addEventListener("submit", async function (event) {
            event.preventDefault();

            let formData = new FormData(this);
            let uploadUrl = "upload_file";

            try {
                const response = await fetch(uploadUrl, {
                    method: "POST",
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();

                    // Show uploaded file name only after successful upload
                    let fileInput = document.getElementById("fileInput");
                    if (fileInput.files.length > 0) {
                        displayFileName(fileInput.files[0].name);
                    }

                    clearErrorTable();

                    if (result.failed_rows && result.failed_rows.length > 0) {
                        showToast("✅ File uploaded successfully. Failed rows are given below.", "success");
                        showErrorTable(result.failed_rows);
                        document.getElementById("failedRowsMessage").classList.remove("hidden");
                    } else {
                        showToast("✅ File uploaded successfully. No errors found.", "success");
                        document.getElementById("failedRowsMessage").classList.add("hidden");
                    }

                    // Clear file input after successful submission
                    document.getElementById("fileInput").value = "";
                } else {
                    const errorResult = await response.json();
                    showToast("❌ File upload failed: " + (errorResult.error || "Unknown error"), "error");
                }
            } catch (error) {
                console.error("Error:", error);
                showToast("⚠️ An error occurred while uploading.", "error");
            }
        });

        function showErrorTable(errors) {
            let errorTable = document.getElementById("errorTable");
            let errorTableBody = document.getElementById("errorTableBody");

            errorTableBody.innerHTML = "";

            errors.forEach(error => {
                let row = `<tr>
                    <td>${error.row}</td>
                    <td>${error.error}</td>
                </tr>`;
                errorTableBody.innerHTML += row;
            });

            errorTable.classList.remove("hidden");
        }

        function clearErrorTable() {
            document.getElementById("errorTableBody").innerHTML = "";
            document.getElementById("errorTable").classList.add("hidden");
            document.getElementById("failedRowsMessage").classList.add("hidden");
        }

        function showToast(message, type) {
            let toast = document.getElementById("toast");
            toast.innerText = message;
            toast.style.backgroundColor = type === "success" ? "#007bff" : "#dc3545";
            toast.style.display = "block";
            setTimeout(() => { toast.style.display = "none"; }, 4000);
        }

        function displayFileName(fileName) {
            let uploadedFileName = document.getElementById("uploadedFileName");

            if (fileName) {
                uploadedFileName.textContent = "📂 Uploaded File: " + fileName;
                uploadedFileName.classList.remove("hidden");
            }
        }
    </script>
{% endblock %}
