{% extends "sqladmin/layout.html" %}
{% block head %}
    <title>Rule file Upload Tool</title>
    <meta name="description" content="File Upload">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.3.1/dist/jquery.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/fomantic-ui@2.7.6/dist/semantic.min.css">
    <script src="https://cdn.jsdelivr.net/npm/fomantic-ui@2.7.6/dist/semantic.min.js"></script>
    <style>#price_push_confirmation {text-align:center;} </style>

{% endblock %}

{% block content %}
    <div class="ui container">
        <h1 class="ui center aligned header" style="margin-top: 32px;">
            Manual price push
        </h1>
        <form class="ui form " method=POST id="price_push_form" >
            <label for="hotel_ids">Hotel IDs( comma seperated):</label><br>
            <input type="text" id="hotel_ids" name="hotel_ids"><br>
            <label for="abw_start">ABW Start:</label><br>
            <input type="text" id="abw_start" name="abw_start"><br>
            <label for="abw_end">ABW End:</label><br>
            <input type="text" id="abw_end" name="abw_end"><br>

        </form>
        <dialog id="price_push_confirmation">
            <p> Are you sure you want to push prices? </p>
            <div>
                <button class="ui grey basic button " type="submit" style="margin-top: 50px;" id="pricepushbtnfinal">Proceed</button>
                <button class="ui grey basic button " type="submit" style="margin-top: 50px;margin-left:20px" id="cancelbtnfinal">Cancel</button>
            </div>
        </dialog>
        <button class="ui grey basic button " type="submit" style="margin-top: 20px;margin-left: 47%" id="pricepushbtn">Push prices</button>
        <script>
            document.body.onload = initPage

            function initPage(){
                $("#pricepushbtn").click(displayConfirmationDialog);
                $("#pricepushbtnfinal").click(submitform);
                $("#cancelbtnfinal").click(closeConfirmationDialog);
            }
            function displayConfirmationDialog(){
                $("#price_push_confirmation").show()
            }
            function closeConfirmationDialog(){
                $("#price_push_confirmation").hide()
            }
            function submitform(){
                const form = document.forms.namedItem("price_push_form");
                const formData = new FormData(form);
                var object = {};
                formData.forEach(function(value, key){
                    object[key] = value;
                });
                var json = JSON.stringify(object);
                $.ajax({type: "POST",
                     headers:{ 'Content-Type': 'application/json' },
                     url: "",
                     data: json,
                     success: function(response){
                        error = response['error']
                        if(!$.isEmptyObject(error)){
                             alert(response['response']+ error)
                        }else{
                             alert(response['response'])
                        }
                    },
                    error: function(response){
                            alert("Something Went Wrong \n "+ response.responseJSON["detail"]);
                    },
                })
                $("#price_push_confirmation").hide()
            }
        </script>
    </div>
{% endblock %}
