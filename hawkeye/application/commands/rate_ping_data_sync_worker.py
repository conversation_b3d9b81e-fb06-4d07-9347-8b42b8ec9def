#!/usr/bin/env python

import logging

import click
import sentry_sdk

from hawkeye.app import create_app
from hawkeye.application.services.rate_ping_data_sync_service import (
    RatePingDataSyncService,
)
from hawkeye.globals import worker_context
from hawkeye.infrastructure.database.db_engine import setup_tenant_sessions
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command()
@click.option("--tenant_id", help="Tenant ID for which this command should run.", required=True)
@click.option("--hours", help="Hours for which this command should run.", required=False)
@inject(rate_sync_service=RatePingDataSyncService)
def sync_rate_ping_data(rate_sync_service, tenant_id, hours=3):
    try:
        setup_tenant_sessions(tenant_id=tenant_id)
        worker_context.set_tenant_id(tenant_id)
        rate_sync_service.sync_data(int(hours))
    except Exception as e:
        click.echo(f"Failed to sync data. Error {e}")
        sentry_sdk.capture_exception(e)


if __name__ == "__main__":
    app = create_app()
    sync_rate_ping_data()
