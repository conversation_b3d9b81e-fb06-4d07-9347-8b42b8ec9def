#!/usr/bin/env python

import logging

import click

from hawkeye.app import create_app
from hawkeye.application.consumers.price_alert_consumer import PriceAlertConsumer
from hawkeye.application.services.price_alert_service import PriceAlertService
from hawkeye.globals import worker_context
from object_registry import inject
from hawkeye.infrastructure.database.db_engine import setup_tenant_sessions
from treebo_commons.multitenancy.tenant_client import TenantClient

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should run.",
    default=TenantClient.get_default_tenant(),
)
@inject(price_alert_service=PriceAlertService)
def start_price_alert_worker(price_alert_service, tenant_id=TenantClient.get_default_tenant()):
    click.echo(f"Tenant ID: {tenant_id}")
    setup_tenant_sessions(tenant_id=tenant_id)
    worker_context.set_tenant_id(tenant_id)
    consumer = PriceAlertConsumer(price_alert_service, tenant_id)
    consumer.start_consumer()


if __name__ == '__main__':
    app = create_app()
    start_price_alert_worker()
