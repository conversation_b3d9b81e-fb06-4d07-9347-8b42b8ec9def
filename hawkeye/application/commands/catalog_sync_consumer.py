#!/usr/bin/env python
import logging

import click
from treebo_commons.multitenancy.tenant_client import TenantClient

from hawkeye.app import create_app
from hawkeye.application.consumers.catalog_consumer import CatalogServiceConsumer
from hawkeye.application.services.catalog_service import CatalogService
from hawkeye.globals import worker_context
from hawkeye.infrastructure.database.db_engine import setup_tenant_sessions
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@inject(catalog_service=CatalogService)
def start_catalog_sync_worker(
    catalog_service, tenant_id
):
    click.echo(f"Tenant ID: {tenant_id}")
    setup_tenant_sessions(tenant_id=tenant_id)
    worker_context.set_tenant_id(tenant_id)
    consumer = CatalogServiceConsumer(catalog_service, tenant_id)
    consumer.start_consumer()


if __name__ == '__main__':
    app = create_app()
    start_catalog_sync_worker()
