#!/usr/bin/env python

import logging

import click
from hawkeye.infrastructure.database.db_engine import setup_tenant_sessions
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.utils.dateutils import current_date, add

from hawkeye.app import create_app
from hawkeye.application.services.catalog_service import CatalogService
from hawkeye.application.services.inventory_service import InventoryService
from hawkeye.constants.hawkeye_constant import INVENTORY_SYNC_DAYS
from hawkeye.globals import worker_context
from hawkeye.utils.collectionutils import chunks
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    "--hotel_ids_str",
    help="Hotel IDs for which this command should run."
)
@click.option(
    "--for_days",
    help="Days for which properties should be synced",
    type=int
)
@inject(catalog_service=CatalogService,
        inventory_service=InventoryService)
def trigger_property_migration(tenant_id, hotel_ids_str, catalog_service, inventory_service, for_days):
    click.echo(f"Tenant ID: {tenant_id}")
    setup_tenant_sessions(tenant_id=tenant_id)
    worker_context.set_tenant_id(tenant_id)
    if not hotel_ids_str:
        logger.info("hotel_ids are not provided running property for all catalog hotels")
        eligible_hotel_ids = catalog_service.get_eligible_hotels_for_trigger_migration(hotel_ids=None)
    else:
        eligible_hotel_ids = catalog_service.get_eligible_hotels_for_trigger_migration(hotel_ids_str.split(","))
    for chunk in chunks(eligible_hotel_ids, 20):
        catalog_service.save_hotels_from_catalog(chunk)
        inventory_service.save_inventory_from_crs(chunk, from_date=current_date(),
                                                  to_date=add(current_date(), days=for_days or INVENTORY_SYNC_DAYS))


if __name__ == '__main__':
    app = create_app()
    trigger_property_migration()
