import logging

import newrelic.agent
import sentry_sdk
from sentry_sdk.integrations.serverless import serverless_function
from treebo_commons.multitenancy.tenant_client import TenantClient

from hawkeye.application.decorators import request_middleware
from hawkeye.application.services.price_alert_service import PriceAlertService
from hawkeye.domain.entities.price_alert import PriceAlert
from hawkeye.infrastructure.consumers.base_consumer import BaseRMQConsumer
from hawkeye.infrastructure.consumers.consumer_config import PriceAlertConsumerConfig


logger = logging.getLogger(__name__)


class PriceAlertConsumer(BaseRMQConsumer):
    def __init__(
        self, price_alert_service: PriceAlertService, tenant_id=TenantClient.get_default_tenant()
    ):
        super().__init__(PriceAlertConsumerConfig(tenant_id))
        logger.info("Listening to RMQ on host: %s from queue: %s", self.connection, self.queue)
        self.price_alert_service = price_alert_service

    @request_middleware
    @serverless_function
    @newrelic.agent.background_task(name='hawkeye.price_alert_consumer.process_message')
    def process_message(self, consumer_event, message):
        alert_id = consumer_event.get("id")
        logger.info(
            f"Price alert message called for hotel: {consumer_event['hotel_id']}, id {alert_id}"
        )

        try:
            price_alert = PriceAlert(**consumer_event)
            self.price_alert_service.send_price_alert(price_alert)
        except Exception as exc:
            logger.info(f"Error in consuming the price alert {alert_id} from price alert consumer: {exc}")
            sentry_sdk.capture_exception(exc)
            message.reject()
            return

        message.ack()
        logger.info(f"Price alert sent for {alert_id}. Message acknowledged")
