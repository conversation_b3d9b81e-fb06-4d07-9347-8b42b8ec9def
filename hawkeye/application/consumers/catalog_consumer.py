import logging

from sentry_sdk.integrations.serverless import serverless_function
from treebo_commons.multitenancy.tenant_client import TenantClient

from hawkeye.application.decorators import request_middleware
from hawkeye.application.services.catalog_service import CatalogService
from hawkeye.application.services.dtos.catalog_dtos import CatalogHotelDto, HotelRoomTypeDto, RoomRackRateDto
from hawkeye.constants.hawkeye_constant import CatalogEvents
from hawkeye.infrastructure.consumers.base_consumer import BaseRMQConsumer
from hawkeye.infrastructure.consumers.consumer_config import CatalogConfig
from hawkeye.infrastructure.telemetry.decorators import background_task

logger = logging.getLogger(__name__)


class CatalogServiceConsumer(BaseRMQConsumer):
    def __init__(self, catalog_service: CatalogService, tenant_id=TenantClient.get_default_tenant()):
        super().__init__(CatalogConfig(tenant_id))
        logger.info(
            f"Listening to RMQ on host: {self.connection} from queue: {self.queue}"
        )
        self.catalog_service = catalog_service

    @request_middleware
    @serverless_function
    @background_task(name='start_catalog_consumer')
    def process_message(self, body, message):
        obj = body
        logger.info("Catalog process_message called for entity: %s", obj.get("entity"))

        try:
            if obj.get("entity") == CatalogEvents.PROPERTY.value:
                self.process_property_message(obj)

            if obj.get("entity") == CatalogEvents.ROOM_TYPE_CONFIG.value:
                self.process_room_type_config_message(obj)

            if obj.get("entity") == CatalogEvents.ROOM_RACK_RATE.value:
                self.process_rack_rate_message(obj)

        except Exception as exc:
            logger.info(f"Error in consuming the messages from catalog consumer: {exc}")
            message.reject()
            return

        message.ack()
        logger.info("Catalog message process complete. Message acknowledged")

    def process_property_message(self, obj):
        catalog_hotel_dto = CatalogHotelDto.create_from_catalog_data(obj.get("data"))
        if obj.get("operation_type") == CatalogEvents.OperationTypes.CREATE.value:
            self.catalog_service.add_hotel(catalog_hotel_dto)
        elif obj.get("operation_type") == CatalogEvents.OperationTypes.UPDATE.value:
            self.catalog_service.update_hotel(catalog_hotel_dto)

    def process_room_type_config_message(self, obj):
        hotel_room_type_dto = HotelRoomTypeDto.create_from_catalog_data(
            obj.get("cs_property_id"), obj.get("data")
        )
        if obj.get("operation_type") == CatalogEvents.OperationTypes.CREATE.value:
            self.catalog_service.add_room_type_config(hotel_room_type_dto)
        elif obj.get("operation_type") == CatalogEvents.OperationTypes.UPDATE.value:
            self.catalog_service.update_room_type_config(hotel_room_type_dto)

    def process_rack_rate_message(self, obj):
        room_rack_rate_dto = RoomRackRateDto.create_from_catalog_data(obj.get('data'))
        if obj.get('operation_type') in [CatalogEvents.OperationTypes.CREATE.value,
                                         CatalogEvents.OperationTypes.UPDATE.value]:
            self.catalog_service.update_rack_rate(room_rack_rate_dto)
