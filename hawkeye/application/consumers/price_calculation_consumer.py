import logging

import sentry_sdk
from sentry_sdk.integrations.serverless import serverless_function
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.utils import dateutils

from hawkeye.application.decorators import request_middleware
from hawkeye.application.services.price_calculation_service import PriceCalculationService
from hawkeye.common.slack_alert_helpers import SlackAlert
from hawkeye.domain.entities.base_room_type_price_rule import BaseRoomTypePriceRule
from hawkeye.domain.entities.base_room_type_sku_price_rule import BaseRoomTypeSkuPriceRule
from hawkeye.domain.entities.incremental_price_rule import IncrementalPriceRule
from hawkeye.domain.entities.price_calculation_event import PriceCalculationEvent
from hawkeye.domain.entities.competitive_price_weightage import CompetitivePriceWeightage
from hawkeye.domain.entities.compset_pricing_threshold import CompsetPricingThreshold
from hawkeye.infrastructure.consumers.base_consumer import BaseRMQConsumer
from hawkeye.infrastructure.consumers.consumer_config import PriceCalculationConsumerConfig
from hawkeye.infrastructure.exception import (
    RackRateMissingError,
    NegativeFinalPriceError,
    MissingPriceBoundaryRuleError
)
from hawkeye.infrastructure.telemetry.decorators import background_task

logger = logging.getLogger(__name__)


class PriceCalculationConsumer(BaseRMQConsumer):
    def __init__(
        self, price_calculation_service: PriceCalculationService, tenant_id=TenantClient.get_default_tenant()
    ):
        super().__init__(PriceCalculationConsumerConfig(tenant_id))
        logger.info("Listening to RMQ on host: %s from queue: %s", self.connection, self.queue)
        self.price_calculation_service = price_calculation_service

    def get_consumers(self, Consumer, channel):
        """
        Override the base method to set prefetch_count=1 for this consumer.
        """
        return [Consumer(queues=[self.queue], callbacks=[self.process_message], prefetch_count=1)]


    @request_middleware
    @serverless_function
    @background_task(name='start_price_calculation_consumer')
    def process_message(self, consumer_event, message):
        start_time = dateutils.current_datetime()
        logger.info(
            f"Price calculation process message called for hotel: {consumer_event['hotel_id']}, "
            f"ABW {consumer_event['abw_start']}-{consumer_event['abw_end']}. Current Datetime: {str(start_time)}"
        )

        try:
            price_calculation_event = PriceCalculationEvent(**consumer_event)

            self.price_calculation_service.calculate_rule_based_prices(
                price_calculation_event.hotel_id,
                price_calculation_event.abw_start,
                price_calculation_event.abw_end,
                price_calculation_event.price_trigger_id,
                [BaseRoomTypePriceRule.from_json(rule) for rule in price_calculation_event.base_room_type_price_rules],
                [IncrementalPriceRule.from_json(rule) for rule in price_calculation_event.incremental_price_rules],
                [
                    BaseRoomTypeSkuPriceRule.from_json(rule)
                    for rule in price_calculation_event.base_room_type_sku_price_rules
                ],
                [CompetitivePriceWeightage.from_json(rule) for rule in price_calculation_event.competitive_price_weightages],
                [CompsetPricingThreshold.from_json(threshold) for threshold in price_calculation_event.compset_pricing_thresholds],
            )
        except RackRateMissingError as exc:
            SlackAlert.send_alert(f"Skipping calculation for hotel: {consumer_event['hotel_id']} due to missing "
                                  f"rack rate and occupancy pricing rule")
            logger.exception(f"Error in consuming the messages from price calculation consumer: {exc}")
            message.reject()
            return
        except (NegativeFinalPriceError, MissingPriceBoundaryRuleError)as exc:
            SlackAlert.send_alert(f"Skipping calculation for hotel: {consumer_event['hotel_id']} msg={exc.message}")
            message.reject()
            return
        except Exception as exc:
            msg = f"Skipping calculation for hotel: {consumer_event['hotel_id']} msg={str(exc)}"
            SlackAlert.send_alert(msg)
            sentry_sdk.capture_exception(exc)
            message.reject()
            return
        message.ack()
        logger.info(
            f"Price calculation complete. TimeTaken{(dateutils.current_datetime() - start_time).seconds} seconds"
        )
