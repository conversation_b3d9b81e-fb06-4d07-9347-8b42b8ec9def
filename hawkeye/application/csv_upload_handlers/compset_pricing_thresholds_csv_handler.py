import codecs
import csv
import logging
from collections import defaultdict

import sentry_sdk
from treebo_commons.utils import dateutils

from hawkeye.application.csv_upload_handlers.dtos.compset_pricing_threshold_dto import (
    CompsetPricingThresholdDto,
)
from hawkeye.application.decorators import session_manager
from hawkeye.application.services.catalog_service import CatalogService
from hawkeye.common.slack_alert_helpers import SlackAlert
from hawkeye.constants.hawkeye_constant import RuleFileUploadStatus
from hawkeye.domain.entities.rule_file_upload import RuleFileUpload
from hawkeye.domain.factory.compset_pricing_threshold_factory import (
    CompsetPricingThresholdFactory,
)
from hawkeye.infrastructure.exception import InvalidFileTypeError, InvalidHotelIdError
from hawkeye.infrastructure.repositories.compset_pricing_threshold_repository import (
    CompsetPricingThresholdRepository,
)
from hawkeye.infrastructure.repositories.hotel_config_repository import (
    HotelConfigRepository,
)
from hawkeye.infrastructure.repositories.rule_file_repository import RuleFileRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CompsetPricingThresholdRepository,
        HotelConfigRepository,
        RuleFileRepository,
        CatalogService,
    ]
)
class CompsetPricingThresholdCSVHandler:
    required_csv_columns = [
        "hotel_id",
        "stay_start",
        "stay_end",
        "increased_percentage_threshold",
        "decreased_percentage_threshold",
    ]

    def __init__(
        self,
        compset_pricing_threshold_repository,
        hotel_config_repository,
        rule_file_repository,
        catalog_service,
    ):
        self.compset_pricing_threshold_repository = compset_pricing_threshold_repository
        self.hotel_config_repository = hotel_config_repository
        self.rule_file_repository = rule_file_repository
        self.catalog_service = catalog_service

    def validate_uploaded_file(self, uploaded_file):
        if not uploaded_file.filename.endswith(".csv"):
            raise InvalidFileTypeError
        uploaded_file.file.seek(0)
        csv_file = csv.DictReader(codecs.iterdecode(uploaded_file.file, "utf-8"))
        file_columns = csv_file.fieldnames
        missing_columns = [
            column for column in self.required_csv_columns if column not in file_columns
        ]
        if missing_columns:
            raise Exception(
                f"Missing columns: {', '.join(missing_columns)}. Please fix the CSV and then upload."
            )

    def process_file(self, uploaded_file, user_email):
        self.validate_uploaded_file(uploaded_file)
        uploaded_file.file.seek(0)
        csv_file = csv.DictReader(codecs.iterdecode(uploaded_file.file, "utf-8"))
        file_id = self.save_rule_file(uploaded_file, user_email)
        failed_rows = []
        try:
            failed_rows = self.process_rules(csv_file, file_id)
        except Exception as e:
            self.update_rule_file_status(file_id, RuleFileUploadStatus.FAILED)
            sentry_sdk.capture_exception(e)
            raise e
        else:
            self.update_rule_file_status(file_id, RuleFileUploadStatus.SUCCESS)
        return failed_rows

    @session_manager(commit=True)
    def save_rule_file(self, uploaded_file, user_email):
        rule_file = RuleFileUpload(
            file_name=uploaded_file.filename,
            uploaded_by=user_email,
            status=RuleFileUploadStatus.PENDING,
            path="",
        )
        return self.rule_file_repository.save(rule_file)

    @session_manager(commit=True)
    def update_rule_file_status(self, file_id, status):
        rule_file = self.rule_file_repository.load_for_update(file_id)
        rule_file.status = status
        self.rule_file_repository.update_rule_file(rule_file)

    def process_rules(self, csv_file, file_id):
        failed_rows = []
        pricing_threshold_rule_dtos_by_hotel_id = defaultdict(list)
        valid_hotels = self.catalog_service.get_hotel_ids()

        hotel_stay_dates_to_row = defaultdict(list)
        for row in csv_file:
            try:
                row = {key: value.strip() if isinstance(value, str) else value for key, value in row.items()}
                hotel_id = row["hotel_id"]
                if hotel_id not in valid_hotels:
                    raise InvalidHotelIdError(description=f"{hotel_id} is invalid.")
                stay_start = dateutils.ymd_str_to_date(row["stay_start"])
                stay_end = dateutils.ymd_str_to_date(row["stay_end"])
                for stay_date in dateutils.date_range(stay_start, stay_end, end_inclusive=True):
                    stay_date = str(stay_date)
                    key = (hotel_id, stay_date)
                    hotel_stay_dates_to_row[key].append(csv_file.line_num - 1)

                    if len(hotel_stay_dates_to_row[key]) > 1:
                        continue
                    row.update(file_id=file_id, stay_date=stay_date)
                    dto = CompsetPricingThresholdDto(**row)
                    pricing_threshold_rule_dtos_by_hotel_id[hotel_id].append(dto)

            except Exception as e:
                logger.exception(e)
                failed_rows.append(dict(row=csv_file.line_num - 1, error=str(e)))
                SlackAlert.send_alert(
                    f"Failed to process row number: {csv_file.line_num -1}. Error: {e}"
                )

        # Ignore rows which are having same stay_date and hotel_id
        for (hotel_id, stay_date), row_numbers in hotel_stay_dates_to_row.items():
            if len(row_numbers) > 1:
                for row_number in row_numbers:
                    failed_rows.append(
                        dict(
                            row=row_number,
                            error=f"Duplicate row found for hotel_id {hotel_id} and stay_date {stay_date}.",
                        )
                    )
                pricing_threshold_rule_dtos_by_hotel_id[hotel_id] = [
                    dto
                    for dto in pricing_threshold_rule_dtos_by_hotel_id[hotel_id]
                    if str(dto.stay_date) != stay_date
                ]

        self.save_rules(pricing_threshold_rule_dtos_by_hotel_id)
        return failed_rows

    @session_manager(commit=True)
    def save_rules(self, pricing_threshold_rules_by_hotel_id):
        for hotel_id, threshold_rule_dtos in pricing_threshold_rules_by_hotel_id.items():
            stays_dates = [dto.stay_date for dto in threshold_rule_dtos]
            existing_thresholds = self.compset_pricing_threshold_repository.load_all_for_hotel(
                hotel_id, stay_dates=stays_dates
            )
            existing_thresholds_by_date = defaultdict()
            for threshold in existing_thresholds:
                existing_thresholds_by_date[threshold.stay_date] = threshold
                threshold.mark_deleted()
            self.compset_pricing_threshold_repository.update_all(existing_thresholds)

            compset_pricing_thresholds = []
            for rule_dto in threshold_rule_dtos:
                new_threshold = CompsetPricingThresholdFactory.create_compset_pricing_threshold(
                    rule_dto
                )
                if rule_dto.stay_date in existing_thresholds_by_date:
                    new_threshold.normalized_competitive_price_avg = existing_thresholds_by_date[
                        rule_dto.stay_date
                    ].normalized_competitive_price_avg
                compset_pricing_thresholds.append(new_threshold)

            self.compset_pricing_threshold_repository.create_all(compset_pricing_thresholds)
