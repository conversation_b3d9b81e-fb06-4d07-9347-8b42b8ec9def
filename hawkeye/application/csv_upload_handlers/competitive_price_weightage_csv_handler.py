import codecs
import csv
import logging
from collections import defaultdict

import sentry_sdk

from hawkeye.application.decorators import session_manager
from hawkeye.application.services.catalog_service import CatalogService
from hawkeye.application.csv_upload_handlers.dtos.competitive_price_weightage_dto import (
    CompetitivePriceWeightageDto,
)
from hawkeye.common.slack_alert_helpers import SlackAlert
from hawkeye.constants.hawkeye_constant import RuleFileUploadStatus
from hawkeye.domain.entities.rule_file_upload import RuleFileUpload
from hawkeye.domain.factory.competitive_weightage_factory import (
    CompetitivePriceWeightageFactory,
)
from hawkeye.infrastructure.exception import InvalidFileTypeError, InvalidHotelIdError
from hawkeye.infrastructure.repositories.competitive_price_weightage_repository import (
    CompetitivePriceWeightageRepository,
)
from hawkeye.infrastructure.repositories.hotel_config_repository import (
    HotelConfigRepository,
)
from hawkeye.infrastructure.repositories.rule_file_repository import RuleFileRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CompetitivePriceWeightageRepository,
        HotelConfigRepository,
        RuleFileRepository,
        CatalogService,
    ]
)
class CompetitivePriceWeightageCSVHandler:
    required_csv_columns = [
        "hotel_id",
        "occupancy_start",
        "occupancy_end",
        "lower_competitive_price_multiplier",
        "higher_competitive_price_multiplier",
    ]

    def __init__(
        self,
        competitive_price_weightage_repository,
        hotel_config_repository,
        rule_file_repository,
        catalog_service,
    ):
        self.competitive_price_weightage_repository = competitive_price_weightage_repository
        self.hotel_config_repository = hotel_config_repository
        self.rule_file_repository = rule_file_repository
        self.catalog_service = catalog_service

    def validate_uploaded_file(self, uploaded_file):
        if not uploaded_file.filename.endswith(".csv"):
            raise InvalidFileTypeError
        uploaded_file.file.seek(0)
        csv_file = csv.DictReader(codecs.iterdecode(uploaded_file.file, "utf-8"))
        file_columns = csv_file.fieldnames
        missing_columns = [
            column for column in self.required_csv_columns if column not in file_columns
        ]
        if missing_columns:
            raise Exception(
                f"Missing columns: {', '.join(missing_columns)}. Please fix the CSV and then upload."
            )

    def process_file(self, uploaded_file, user_email):
        self.validate_uploaded_file(uploaded_file)
        uploaded_file.file.seek(0)
        csv_file = csv.DictReader(codecs.iterdecode(uploaded_file.file, "utf-8"))
        file_id = self.save_rule_file(uploaded_file, user_email)
        failed_rows = []
        try:
            failed_rows = self.process_rules(csv_file, file_id)
        except Exception as e:
            self.update_rule_file_status(file_id, RuleFileUploadStatus.FAILED)
            sentry_sdk.capture_exception(e)
            raise e
        else:
            self.update_rule_file_status(file_id, RuleFileUploadStatus.SUCCESS)
        return failed_rows

    @session_manager(commit=True)
    def save_rule_file(self, uploaded_file, user_email):
        rule_file = RuleFileUpload(
            file_name=uploaded_file.filename,
            uploaded_by=user_email,
            status=RuleFileUploadStatus.PENDING,
            path=''
        )
        return self.rule_file_repository.save(rule_file)

    @session_manager(commit=True)
    def update_rule_file_status(self, file_id, status):
        rule_file = self.rule_file_repository.load_for_update(file_id)
        rule_file.status = status
        self.rule_file_repository.update_rule_file(rule_file)

    def process_rules(self, csv_file, file_id):
        failed_rows = []
        price_weightage_rule_dtos_by_hotel_id = defaultdict(list)
        valid_hotels = self.catalog_service.get_hotel_ids()
        for row in csv_file:
            try:
                hotel_id = row["hotel_id"]
                if hotel_id not in valid_hotels:
                    raise InvalidHotelIdError(description=f"{hotel_id} is invalid.")
                row.update(file_id=file_id)
                dto = CompetitivePriceWeightageDto(**row)
                price_weightage_rule_dtos_by_hotel_id[hotel_id].append(dto)
            except Exception as e:
                logger.exception(e)
                failed_rows.append(dict(row=csv_file.line_num-1, error=str(e)))
                SlackAlert.send_alert(f"Failed to process row number: {csv_file.line_num -1}. Error: {e}")

        self.save_rules(price_weightage_rule_dtos_by_hotel_id)
        return failed_rows

    @session_manager(commit=True)
    def save_rules(self, price_weightage_rules_by_hotel_id):
        for hotel_id, weightage_rule_dtos in price_weightage_rules_by_hotel_id.items():
            existing_price_weightages = (
                self.competitive_price_weightage_repository.load_all_for_update(hotel_id)
            )
            if existing_price_weightages:
                for existing_price_weightage in existing_price_weightages:
                    existing_price_weightage.mark_deleted()
                self.competitive_price_weightage_repository.update_all(existing_price_weightages)
            competitive_price_weightages = [
                CompetitivePriceWeightageFactory.create_competitive_price_weightage(rule_dto)
                for rule_dto in weightage_rule_dtos
            ]
            self.competitive_price_weightage_repository.create_all(competitive_price_weightages)
