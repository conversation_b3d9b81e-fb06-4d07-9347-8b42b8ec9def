import codecs
import logging
import csv

import sentry_sdk
from pydantic.fields import defaultdict

from hawkeye.application.csv_upload_handlers.dtos.hotel_config_dto import HotelConfigDto
from hawkeye.application.decorators import session_manager
from hawkeye.application.services.catalog_service import CatalogService
from hawkeye.common.slack_alert_helpers import Slack<PERSON>lert
from hawkeye.constants.hawkeye_constant import RuleFileUploadStatus
from hawkeye.domain.entities.rule_file_upload import RuleFileUpload
from hawkeye.infrastructure.exception import InvalidFileTypeError, InvalidHotelIdError
from hawkeye.infrastructure.repositories.hotel_config_repository import HotelConfigRepository
from hawkeye.infrastructure.repositories.rule_file_repository import RuleFileRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        HotelConfigRepository,
        CatalogService,
        RuleFileRepository
    ]
)
class EnableHotelRatePingCSVHandler:
    required_csv_columns = [
        "hotel_id",
        "revenue_poc_emails",
        "is_competitor_price_enabled"
    ]

    def __init__(
            self,
            hotel_config_repository,
            catalog_service,
            rule_file_repository,
    ):
        self.hotel_config_repository = hotel_config_repository
        self.catalog_service = catalog_service
        self.rule_file_repository = rule_file_repository

    def validate_uploaded_file(self, uploaded_file):
        if not uploaded_file.filename.endswith(".csv"):
            raise InvalidFileTypeError
        uploaded_file.file.seek(0)
        csv_file = csv.DictReader(codecs.iterdecode(uploaded_file.file, "utf-8"))
        file_columns = csv_file.fieldnames
        missing_columns = [
            column for column in self.required_csv_columns if column not in file_columns
        ]
        if missing_columns:
            raise Exception(
                f"Missing columns: {', '.join(missing_columns)}. Please fix the CSV and then upload."
            )

    def process_file(self, uploaded_file, user_email):
        self.validate_uploaded_file(uploaded_file)
        uploaded_file.file.seek(0)
        csv_file = csv.DictReader(codecs.iterdecode(uploaded_file.file, "utf-8"))
        file_id = self.save_rule_file(uploaded_file, user_email)
        try:
            failed_rows = self.process_rules(csv_file, file_id)
        except Exception as e:
            self.update_rule_file_status(file_id, RuleFileUploadStatus.FAILED)
            sentry_sdk.capture_exception(e)
            raise e

        self.update_rule_file_status(file_id, RuleFileUploadStatus.SUCCESS)
        return failed_rows

    def process_rules(self, csv_file, file_id):
        failed_rows = []
        valid_hotels = self.catalog_service.get_hotel_ids()
        hotel_id_to_hotel_config_dto = {}
        hotel_id_to_row = defaultdict(list)
        for row in csv_file:
            try:
                hotel_id = row["hotel_id"]
                if hotel_id not in valid_hotels:
                    raise InvalidHotelIdError(description=f"{hotel_id} is invalid.")
                hotel_id_to_row[hotel_id].append(csv_file.line_num - 1)
                if len(hotel_id_to_row[hotel_id]) > 1:
                    continue
                row.update(file_id=file_id)
                dto = HotelConfigDto(**row)
                hotel_id_to_hotel_config_dto[dto.hotel_id] = dto

            except Exception as e:
                logger.exception(e)
                failed_rows.append(dict(row=csv_file.line_num - 1, error=str(e)))
                SlackAlert.send_alert(f"Failed to process row number: {csv_file.line_num - 1}. Error: {e}")

        for hotel_id, row_numbers in hotel_id_to_row.items():
            if len(row_numbers) > 1:
                for row_number in row_numbers:
                    failed_rows.append(
                        dict(
                            row=row_number,
                            error=f"Duplicate row found for hotel_id {hotel_id}.",
                        )
                    )
        self.save_rules(hotel_id_to_hotel_config_dto)
        return failed_rows

    @session_manager(commit=True)
    def save_rule_file(self, uploaded_file, user_email):
        rule_file = RuleFileUpload(
            file_name=uploaded_file.filename,
            uploaded_by=user_email,
            status=RuleFileUploadStatus.PENDING,
            path=''
        )
        return self.rule_file_repository.save(rule_file)

    @session_manager(commit=True)
    def update_rule_file_status(self, file_id, status):
        rule_file = self.rule_file_repository.load_for_update(file_id)
        rule_file.status = status
        self.rule_file_repository.update_rule_file(rule_file)

    @session_manager(commit=True)
    def save_rules(self, hotel_id_to_hotel_config_dto):
        hotel_ids = hotel_id_to_hotel_config_dto.keys()
        hotel_configs = self.hotel_config_repository.load_all_for_update(hotel_ids)
        hotel_config_map = {config.hotel_id: config for config in hotel_configs}

        for hotel_id, dto in hotel_id_to_hotel_config_dto.items():
            hotel_config = hotel_config_map.get(hotel_id)
            if not hotel_config:
                continue
            for key, value in dto.dict(exclude_none=True).items():
                setattr(hotel_config, key, value)

        self.hotel_config_repository.update_all(hotel_configs)
