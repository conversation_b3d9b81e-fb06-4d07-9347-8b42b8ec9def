import codecs
import csv
import logging

import sentry_sdk
from pydantic.fields import defaultdict

from hawkeye.application.csv_upload_handlers.dtos.competitive_hotel_mapping_dto import CompetitiveHotelMappingDTO
from hawkeye.application.decorators import session_manager
from hawkeye.application.services.catalog_service import CatalogService
from hawkeye.constants.hawkeye_constant import RuleFileUploadStatus
from hawkeye.domain.entities.rule_file_upload import RuleFileUpload
from hawkeye.infrastructure.exception import InvalidFileTypeError, InvalidHotelIdError, InvalidHotelMappingError
from hawkeye.infrastructure.repositories.competitve_hotel_mapping import (
    CompetitiveHotelMappingRepository,
)
from hawkeye.infrastructure.repositories.rule_file_repository import RuleFileRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CompetitiveHotelMappingRepository,
        RuleFileRepository,
        CatalogService,
    ]
)
class CompetitiveHotelMappingCSVHandler:
    required_csv_columns = [
        "competitive_hotel_id",
        "internal_hotel_id",
        "is_active",
        "normalization_factor",
    ]

    def __init__(
        self,
        competitive_hotel_mapping_repository,
        rule_file_repository,
        catalog_service,
    ):
        self.competitive_hotel_mapping_repository = competitive_hotel_mapping_repository
        self.rule_file_repository = rule_file_repository
        self.catalog_service = catalog_service

    def validate_uploaded_file(self, uploaded_file):
        if not uploaded_file.filename.endswith(".csv"):
            raise InvalidFileTypeError
        uploaded_file.file.seek(0)
        csv_file = csv.DictReader(codecs.iterdecode(uploaded_file.file, "utf-8"))
        file_columns = csv_file.fieldnames
        missing_columns = [
            column for column in self.required_csv_columns if column not in file_columns
        ]
        if missing_columns:
            raise Exception(
                f"Missing columns: {', '.join(missing_columns)}. Please fix the CSV and then upload."
            )

    def process_file(self, uploaded_file, user_email):
        self.validate_uploaded_file(uploaded_file)
        uploaded_file.file.seek(0)
        csv_file = csv.DictReader(codecs.iterdecode(uploaded_file.file, "utf-8"))
        file_id = self.save_rule_file(uploaded_file, user_email)
        failed_rows = []
        try:
            failed_rows = self.process_rules(csv_file, file_id)
        except Exception as e:
            self.update_rule_file_status(file_id, RuleFileUploadStatus.FAILED)
            sentry_sdk.capture_exception(e)
            raise e
        else:
            self.update_rule_file_status(file_id, RuleFileUploadStatus.SUCCESS)
        return failed_rows

    @session_manager(commit=True)
    def save_rule_file(self, uploaded_file, user_email):
        rule_file = RuleFileUpload(
            file_name=uploaded_file.filename,
            uploaded_by=user_email,
            status=RuleFileUploadStatus.PENDING,
            path="",
        )
        return self.rule_file_repository.save(rule_file)

    @session_manager(commit=True)
    def update_rule_file_status(self, file_id, status):
        rule_file = self.rule_file_repository.load_for_update(file_id)
        rule_file.status = status
        self.rule_file_repository.update_rule_file(rule_file)

    def process_rules(self, csv_file, file_id):
        failed_rows = []
        valid_hotels = self.catalog_service.get_hotel_ids()
        hotel_ids_pair_to_dto = {}
        hotel_ids_pair_to_row = defaultdict(list)

        for row in csv_file:
            try:
                internal_hotel_id = row["internal_hotel_id"]
                competitive_hotel_id = row["competitive_hotel_id"]

                hotel_mapping = self.competitive_hotel_mapping_repository.load_mapping_for_update(
                    competitive_hotel_id=competitive_hotel_id
                )
                if hotel_mapping and hotel_mapping.internal_hotel_id != internal_hotel_id:
                    raise InvalidHotelMappingError(
                        description=f"Competitive hotel id: {competitive_hotel_id} is already associated with Hotel {internal_hotel_id}"
                    )

                key = (internal_hotel_id, competitive_hotel_id)
                hotel_ids_pair_to_row[key].append(csv_file.line_num - 1)

                if len(hotel_ids_pair_to_row[key]) > 1:
                    continue
                row.update(file_id=file_id)
                dto = CompetitiveHotelMappingDTO(**row)

                if dto.internal_hotel_id not in valid_hotels:
                    raise InvalidHotelIdError(description=f"{dto.internal_hotel_id} is invalid.")

                key = (dto.internal_hotel_id, dto.competitive_hotel_id)
                hotel_ids_pair_to_dto[key] = dto

            except Exception as e:
                failed_rows.append(dict(row=csv_file.line_num - 1, error=str(e)))
                sentry_sdk.capture_exception(e)

        for (internal_hotel_id, competitive_hotel_id), row_numbers in hotel_ids_pair_to_row.items():
            if len(row_numbers) > 1:
                for row_number in row_numbers:
                    failed_rows.append(
                        dict(
                            row=row_number,
                            error=f"Duplicate row found for hotel_id {internal_hotel_id}.",
                        )
                    )
        self.save_rules(hotel_ids_pair_to_dto)
        return failed_rows

    @session_manager(commit=True)
    def save_rules(self, hotel_ids_pair_to_dto):
        updated_mappings = []
        created_mappings = []

        for (internal_hotel_id, competitive_hotel_id), dto in hotel_ids_pair_to_dto.items():
            mapping = (
                self.competitive_hotel_mapping_repository
                .load_mapping_for_update(internal_hotel_id=internal_hotel_id, competitive_hotel_id=competitive_hotel_id)
            )

            if mapping:
                for attr, value in dto.dict(exclude_none=True).items():
                    setattr(mapping, attr, value)
                updated_mappings.append(mapping)
            else:
                mapping = dto.to_entity()
                created_mappings.append(mapping)

        self.competitive_hotel_mapping_repository.update_all(updated_mappings)
        self.competitive_hotel_mapping_repository.create_all(created_mappings)
