from pydantic import BaseModel, validator
from typing import Optional


class CompetitivePriceWeightageDto(BaseModel):
    hotel_id: str
    occupancy_start: float
    occupancy_end: float
    lower_competitive_price_multiplier: float
    higher_competitive_price_multiplier: float
    file_id: Optional[int] = None
    is_deleted: Optional[bool] = False

    @validator("occupancy_end")
    def check_occupancy(cls, occupancy_end, values, **kwargs):
        occupancy_start = values.get("occupancy_start")
        if occupancy_start is not None and occupancy_start > occupancy_end:
            raise ValueError("occupancy_start must be lesser than occupancy_end")
        return occupancy_end

    @classmethod
    def create_from_rule(cls, **kwargs):
        return cls(**kwargs)

