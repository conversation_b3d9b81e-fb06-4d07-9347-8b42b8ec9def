from pydantic import BaseModel, validator
from typing import Optional
from decimal import Decimal, InvalidOperation

from hawkeye.domain.entities.competitive_hotel_mapping import CompetitiveHotelMapping


class CompetitiveHotelMappingDTO(BaseModel):
    competitive_hotel_id: str
    internal_hotel_id: str
    is_active: Optional[bool]
    normalization_factor: Optional[Decimal]
    file_id: Optional[int] = None

    @classmethod
    def create_from_rule(cls, **kwargs):
        return cls(**kwargs)

    def to_entity(self):
        return CompetitiveHotelMapping(
            internal_hotel_id=self.internal_hotel_id,
            competitive_hotel_id=self.competitive_hotel_id,
            is_active=self.is_active,
            normalization_factor=self.normalization_factor,
            file_id=self.file_id,
        )

    @validator('internal_hotel_id', pre=True)
    def validate_internal_hotel_id(cls, v):
        if not v or str(v).strip() == "":
            raise ValueError("Please provide internal_hotel_id")
        return str(v).zfill(7)

    @validator('competitive_hotel_id', pre=True)
    def validate_competitive_hotel_id(cls, v):
        if not v or str(v).strip() == "":
            raise ValueError("Please provide competitive_hotel_id")
        return v

    @validator('normalization_factor', pre=True, always=True)
    def validate_normalization_factor(cls, v):
        if v is None or str(v).strip() == "":
            return Decimal("1")
        try:
            value = Decimal(v)
            if value < 0:
                raise ValueError("Normalization factor cannot be negative.")
            return value
        except (InvalidOperation, TypeError, ValueError):
            raise ValueError("Invalid input for normalization_factor. Please provide a valid non-negative decimal.")
