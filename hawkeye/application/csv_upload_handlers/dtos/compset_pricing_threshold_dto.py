from pydantic import BaseModel, validator
from datetime import date
from typing import Optional
from decimal import Decimal, InvalidOperation


class CompsetPricingThresholdDto(BaseModel):
    hotel_id: str
    stay_date: date
    increased_percentage_threshold: Decimal
    decreased_percentage_threshold: Decimal
    normalized_competitive_price_avg: Optional[Decimal]
    file_id: Optional[int] = None

    @classmethod
    def create_from_rule(cls, **kwargs):
        return cls(**kwargs)

    @validator('increased_percentage_threshold', pre=True)
    def validate_increased_percentage(cls, v):
        try:
            return Decimal(v)
        except (InvalidOperation, TypeError, ValueError):
            raise ValueError(
                "Invalid input for increased_percentage_threshold. Please provide a valid decimal.")

    @validator('decreased_percentage_threshold', pre=True)
    def validate_decreased_percentage(cls, v):
        try:
            return Decimal(v)
        except (InvalidOperation, TypeError, ValueError):
            raise ValueError(
                "Invalid input for decreased_percentage_threshold. Please provide a valid decimal.")

    @validator('normalized_competitive_price_avg', pre=True)
    def validate_normalized_price(cls, v):
        if v is None or v == "":
            return None
        try:
            return Decimal(v)
        except (InvalidOperation, TypeError, ValueError):
            raise ValueError(
                "Invalid input for normalized_competitive_price_avg. Please provide a valid decimal.")

    @validator('hotel_id', pre=True)
    def validate_hotel_id(cls, v):
        if not v or str(v).strip() == "":
            raise ValueError("Please provide a hotel id")
        v = str(v).strip()
        if len(v) < 7:
            v = v.zfill(7)
        return v
