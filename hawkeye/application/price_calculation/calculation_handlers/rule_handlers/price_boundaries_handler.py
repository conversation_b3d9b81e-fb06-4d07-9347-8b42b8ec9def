from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.base_rule_handler import BaseRuleHandler
from hawkeye.constants.hawkeye_constant import RuleType
from hawkeye.domain.entities.room_type_price import RoomTypePrice
from hawkeye.infrastructure.exception import MissingPriceBoundaryRuleError
from object_registry import register_instance


@register_instance()
class PriceBoundariesHandler(BaseRuleHandler):

    @property
    def rule_type(self):
        return RuleType.PRICE_BOUNDARIES

    def get_price_rule(self, price_aggregate, price_rules):
        applicable_price_rules = price_rules.get(self.rule_type.value, [])
        return next(
            (price_rule for price_rule in applicable_price_rules if price_rule.rule_type == self.rule_type.value),
            None
        )

    def perform_calculation(self, price_aggregate, price_rules):
        price_boundary_rule = self.get_price_rule(price_aggregate, price_rules)

        if price_aggregate.room_type_price_entity and price_boundary_rule:
            price_aggregate.room_type_price_entity.apply_price_boundaries_rule(price_boundary_rule)

        return self.next_handler.perform_calculation(price_aggregate, price_rules)

    def perform_extra_adult_price_calculation(self, price_aggregate, price_rules):
        price_boundary_rule = self.get_price_rule(price_aggregate, price_rules)
        if not price_boundary_rule:
            raise MissingPriceBoundaryRuleError
        initial_price = float((price_boundary_rule.start_price + price_boundary_rule.end_price) / 2)
        room_type_price_entity = RoomTypePrice(
            hotel_id=price_aggregate.hotel_id,
            target_date=price_aggregate.target_date,
            input_price=initial_price,
            final_price=initial_price,
            sku_code=price_aggregate.sku_code,
            sku_name=price_aggregate.sku_name,
            rules=[price_boundary_rule],
            is_published=False,
            price_trigger_id=price_aggregate.price_trigger_id,
        )
        price_aggregate.set_room_type_price(room_type_price_entity)
        return self.next_handler.perform_calculation(price_aggregate, price_rules)
