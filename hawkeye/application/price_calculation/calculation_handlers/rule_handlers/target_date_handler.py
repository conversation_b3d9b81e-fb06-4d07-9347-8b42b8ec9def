from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.base_rule_handler import BaseRuleHandler
from hawkeye.constants.hawkeye_constant import RuleType
from object_registry import register_instance


@register_instance()
class TargetDateHandler(BaseRuleHandler):

    @property
    def rule_type(self):
        return RuleType.TARGET_DATE

    def get_price_rule(self, price_aggregate, price_rules):
        applicable_price_rules = price_rules.get(self.rule_type.value, [])
        return next(
            (
                price_rule for price_rule in applicable_price_rules
                if price_rule.config.target_date == price_aggregate.target_date
            ),
            None
        )
