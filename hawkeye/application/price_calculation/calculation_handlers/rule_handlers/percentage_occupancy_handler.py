from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.base_rule_handler import BaseRuleHandler
from hawkeye.application.services.dtos.pricing_rule_dtos import PricingRuleDto
from hawkeye.constants.hawkeye_constant import RuleType

from hawkeye.domain.entities.room_type_price import RoomTypePrice
from hawkeye.domain.factory.base_room_type_price_rule_factory import BaseRoomTypePriceRuleFactory
from hawkeye.domain.price_calculation.aggregates.sku_price_aggregate import SkuPriceAggregate
from hawkeye.infrastructure.exception import MissingOccupancyRuleError
from hawkeye.infrastructure.exception import PercentageOccupancyRangeError
from object_registry import register_instance


@register_instance()
class PercentageOccupancyHandler(BaseRuleHandler):

    @property
    def rule_type(self):
        return RuleType.PERCENTAGE_OCCUPANCY

    def get_price_rule(self, price_aggregate, price_rules):
        applicable_price_rules = price_rules.get(self.rule_type.value, [])
        price_rule = next(
            (
                price_rule for price_rule in applicable_price_rules
                if price_rule.config.occupancy_start <= price_aggregate.hotel_occupancy <= price_rule.config.occupancy_end
            ),
            None
        )
        if not price_rule:
            raise MissingOccupancyRuleError("Price rules not properly configured.")
        return price_rule

    def get_initial_price(self, price_rule, price_aggregate):
        if price_rule.config.occupancy_end - price_rule.config.occupancy_start == 0:
            raise PercentageOccupancyRangeError

        return (
            (
                float(
                    price_rule.end_price - price_rule.start_price
                ) / (
                    price_rule.config.occupancy_end - price_rule.config.occupancy_start
                )
            ) * (
                price_aggregate.hotel_occupancy - price_rule.config.occupancy_start
            )
        ) + float(price_rule.start_price)

    def perform_calculation(self, price_aggregate, price_rules):
        try:
            base_room_type_price_rule = self.get_price_rule(price_aggregate, price_rules)
            initial_price = self.get_initial_price(base_room_type_price_rule, price_aggregate)
        except MissingOccupancyRuleError:
            initial_price = float(price_aggregate.rack_rate)
            price_rule_dto = PricingRuleDto.create_rack_rate_price_rule(hotel_id=price_aggregate.hotel_id,
                                                                        rack_rate=initial_price)
            base_room_type_price_rule = BaseRoomTypePriceRuleFactory.create_pricing_rule(price_rule_dto)
        room_type_price_entity = RoomTypePrice(
            hotel_id=price_aggregate.hotel_id,
            room_type=price_aggregate.room_type_name,
            target_date=price_aggregate.target_date,
            input_price=initial_price,
            final_price=initial_price,
            occupancy_percentage=price_aggregate.hotel_occupancy,
            rules=[base_room_type_price_rule],
            is_published=False,
            price_trigger_id=price_aggregate.price_trigger_id,
            sku_name=f'{price_aggregate.room_type_name}-1'
        )
        price_aggregate.set_room_type_price(room_type_price_entity)

        return self.next_handler.perform_calculation(price_aggregate, price_rules)

    def perform_calculation_for_skus(self, price_aggregate: SkuPriceAggregate, price_rules, room_type_price):
        initial_price = room_type_price
        price_rule_dto = PricingRuleDto.create_rack_rate_price_rule(hotel_id=price_aggregate.hotel_id,
                                                                    rack_rate=initial_price)
        base_room_type_price_rule = BaseRoomTypePriceRuleFactory.create_pricing_rule(price_rule_dto)
        room_type_price_entity = RoomTypePrice(
            hotel_id=price_aggregate.hotel_id,
            sku_code=price_aggregate.sku_code,
            sku_name=price_aggregate.sku_name,
            target_date=price_aggregate.target_date,
            input_price=initial_price,
            final_price=initial_price,
            occupancy_percentage=price_aggregate.hotel_occupancy,
            rules=[base_room_type_price_rule],
            is_published=False,
            price_trigger_id=price_aggregate.price_trigger_id,
        )
        price_aggregate.set_room_type_price(room_type_price_entity)

        try:
            percentage_occupancy_rule = self.get_price_rule(price_aggregate, price_rules)
        except MissingOccupancyRuleError:
            percentage_occupancy_rule = None
        if percentage_occupancy_rule and price_aggregate.room_type_price_entity:
            price_aggregate.room_type_price_entity.apply_price_rule(percentage_occupancy_rule)

        return self.next_handler.perform_calculation(price_aggregate, price_rules)
