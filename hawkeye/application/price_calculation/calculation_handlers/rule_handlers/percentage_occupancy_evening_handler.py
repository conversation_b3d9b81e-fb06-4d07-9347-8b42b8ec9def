import dateutil.parser

from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.base_rule_handler import BaseRuleHandler
from hawkeye.constants.hawkeye_constant import (
    RuleType, PERCENTAGE_OCCUPANCY_EVENING_TIME_LIMIT,
    PERCENTAGE_OCCUPANCY_EVENING_ABW_LIMIT
)
from object_registry import register_instance
from treebo_commons.utils import dateutils


@register_instance()
class PercentageOccupancyEveningHandler(BaseRuleHandler):

    @property
    def rule_type(self):
        return RuleType.PERCENTAGE_OCCUPANCY_EVENING

    def get_price_rule(self, price_aggregate, price_rules):
        current_time = dateutils.current_time()
        # TODO: Use dateutil.isoformat_str_to_datetime, Currently it doesn't support Python3.9
        percentage_occupancy_evening_time = dateutils.to_time(
            dateutils.get_timezone().localize(
                dateutil.parser.parse(PERCENTAGE_OCCUPANCY_EVENING_TIME_LIMIT)
            )
        )

        if current_time < percentage_occupancy_evening_time:
            return

        advance_booking_window = (price_aggregate.target_date - dateutils.current_date()).days
        if advance_booking_window > PERCENTAGE_OCCUPANCY_EVENING_ABW_LIMIT:
            return
        applicable_price_rules = price_rules.get(self.rule_type.value, [])
        return next(
            (
                price_rule for price_rule in applicable_price_rules
                if price_rule.config.occupancy_start <= price_aggregate.hotel_occupancy <= price_rule.config.occupancy_end
            ),
            None
        )
