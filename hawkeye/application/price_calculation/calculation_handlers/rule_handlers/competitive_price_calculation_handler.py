from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.base_rule_handler import <PERSON><PERSON>uleHandler
from hawkeye.constants.hawkeye_constant import IGNORE_COMPETITIVE_PRICE_DIFFERENCE, RuleType
from hawkeye.domain.entities.price_alert import PriceAlert
from hawkeye.common.slack_alert_helpers import Slack<PERSON>lert

from object_registry import register_instance


@register_instance()
class CompetitivePriceHandler(BaseRuleHandler):

    @property
    def rule_type(self):
        return RuleType.COMPETITIVE_PRICE

    def get_price_rule(self, price_aggregate, price_rules):
        ...

    def get_compset_pricing_threshold(self, price_aggregate):
        for compset_pricing_threshold in price_aggregate.compset_pricing_thresholds:
            if compset_pricing_threshold.stay_date == price_aggregate.target_date:
                if not compset_pricing_threshold.normalized_competitive_price_avg:
                    SlackAlert.send_alert(f"Competition Price is not available "
                                          f"for hotel {price_aggregate.hotel_id} on {price_aggregate.target_date}")
                    return None
                return compset_pricing_threshold
        return None

    def get_competitive_price_weightage(self, price_aggregate):
        for competitive_price_weightage in price_aggregate.competitive_price_weightages:
            if (
                    competitive_price_weightage.occupancy_start <= price_aggregate.room_type_price_entity.occupancy_percentage
                    <= competitive_price_weightage.occupancy_end
            ):
                return competitive_price_weightage
        return None

    def get_competition_adjusted_price(self, price_aggregate, compset_pricing_threshold, competitive_price_weightage):
        if compset_pricing_threshold.normalized_competitive_price_avg < price_aggregate.room_type_price_entity.final_price:
            competitive_price_multiplier = competitive_price_weightage.lower_competitive_price_multiplier
        else:
            competitive_price_multiplier = competitive_price_weightage.higher_competitive_price_multiplier
        return (
            competitive_price_multiplier * compset_pricing_threshold.normalized_competitive_price_avg
            + (100 - competitive_price_multiplier) * price_aggregate.room_type_price_entity.final_price
        ) / 100

    def set_price_adjustment_alert(
        self,
        price_aggregate,
        competition_adjusted_price,
        threshold_adjusted_price,
        compset_price_delta,
        alert_threshold_percentage,
    ):
        price_alert = PriceAlert(
            hotel_id=price_aggregate.hotel_id,
            stay_date=price_aggregate.target_date,
            competitive_price=competition_adjusted_price,
            hawkeye_price=price_aggregate.room_type_price_entity.final_price,
            threshold_price=threshold_adjusted_price,
            suggested_change_percentage=compset_price_delta,
            alert_threshold_percentage=alert_threshold_percentage,
        )
        price_aggregate.set_price_alert(price_alert)

    def get_threshold_adjusted_price(self, price_aggregate, competition_adjusted_price, compset_pricing_threshold):
        threshold_adjusted_price = competition_adjusted_price
        compset_price_delta = round(
            abs(price_aggregate.room_type_price_entity.final_price - competition_adjusted_price) * 100
            / price_aggregate.room_type_price_entity.final_price
        )
        alert_threshold_percentage = compset_price_delta
        if (
            competition_adjusted_price < price_aggregate.room_type_price_entity.final_price
            and compset_price_delta > compset_pricing_threshold.decreased_percentage_threshold
        ):
            threshold_adjusted_price = (
                (100 - compset_pricing_threshold.decreased_percentage_threshold)
                * price_aggregate.room_type_price_entity.final_price
            ) / 100
            alert_threshold_percentage = compset_pricing_threshold.decreased_percentage_threshold
        elif (
            competition_adjusted_price > price_aggregate.room_type_price_entity.final_price
            and compset_price_delta > compset_pricing_threshold.increased_percentage_threshold
        ):
            threshold_adjusted_price = (
                (100 + compset_pricing_threshold.increased_percentage_threshold)
                * price_aggregate.room_type_price_entity.final_price
            ) / 100
            alert_threshold_percentage = compset_pricing_threshold.increased_percentage_threshold

        if threshold_adjusted_price != competition_adjusted_price:
            self.set_price_adjustment_alert(
                price_aggregate,
                competition_adjusted_price,
                threshold_adjusted_price,
                compset_price_delta,
                alert_threshold_percentage,
            )

        return round(threshold_adjusted_price, 4)

    def perform_calculation(self, price_aggregate, price_rules):
        if not price_aggregate.competitive_price_weightages or not price_aggregate.compset_pricing_thresholds:
            return self.next_handler.perform_calculation(price_aggregate, price_rules)

        compset_pricing_threshold = self.get_compset_pricing_threshold(price_aggregate)
        competitive_price_weightage = self.get_competitive_price_weightage(price_aggregate)

        if not compset_pricing_threshold or not competitive_price_weightage:
            return self.next_handler.perform_calculation(price_aggregate, price_rules)

        competitive_price_difference = abs(
            price_aggregate.room_type_price_entity.final_price
            - compset_pricing_threshold.normalized_competitive_price_avg
        )
        if competitive_price_difference <= IGNORE_COMPETITIVE_PRICE_DIFFERENCE:
            return self.next_handler.perform_calculation(price_aggregate, price_rules)

        competition_adjusted_price = self.get_competition_adjusted_price(
            price_aggregate, compset_pricing_threshold, competitive_price_weightage)

        threshold_adjusted_price = self.get_threshold_adjusted_price(
            price_aggregate, competition_adjusted_price, compset_pricing_threshold)

        price_aggregate.room_type_price_entity.final_price = threshold_adjusted_price
        price_aggregate.room_type_price_entity.rules.extend(
            (competitive_price_weightage, compset_pricing_threshold)
        )

        return self.next_handler.perform_calculation(price_aggregate, price_rules)
