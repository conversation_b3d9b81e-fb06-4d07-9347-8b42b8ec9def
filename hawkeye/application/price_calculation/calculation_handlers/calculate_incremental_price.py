from typing import Dict

from hawkeye.application.services.dtos.pricing_rule_dtos import PricingRuleDto
from hawkeye.constants.hawkeye_constant import DEFAULT_SUM_FACTOR, SkuNameComponent
from hawkeye.domain.entities.room_type_price import RoomTypePrice
from hawkeye.domain.factory.base_room_type_price_rule_factory import BaseRoomTypePriceRuleFactory
from hawkeye.domain.price_calculation.aggregates.price_aggregate import PriceAggregate
from object_registry import register_instance


@register_instance()
class CalculateIncrementalPriceCommandHandler:

    @staticmethod
    def _get_extra_adult_prices(room_type_name, extra_adult_sku_prices, extra_adult_rack_rate):
        extra_adult_key = f"{room_type_name}-{SkuNameComponent.EXTRA_ADULT}"
        subsequent_key = f"{room_type_name}-{SkuNameComponent.SUBSEQUENT_EXTRA_ADULT}"
        return (
            extra_adult_sku_prices.get(extra_adult_key, extra_adult_rack_rate[extra_adult_key]),
            extra_adult_sku_prices.get(subsequent_key, extra_adult_rack_rate[subsequent_key]),
        )

    @staticmethod
    def _get_price_rule(hotel_id, input_price, extra_adult_price, subsequent_price):
        return BaseRoomTypePriceRuleFactory.create_pricing_rule(
            PricingRuleDto.create_extra_adult_price_rule(
                hotel_id, input_price, extra_adult_price, subsequent_price
            )
        )

    def handle(
        self, price_aggregate: PriceAggregate, extra_adult_sku_prices: Dict[str, float],
        extra_adult_rack_rate: Dict[str, float]
    ):
        price_aggregate.finalize_price(price_aggregate.room_type_price_entity)
        incremental_input_price = price_aggregate.room_type_price_entity.final_price

        room_name = price_aggregate.room_type_name
        extra_adult_price, subsequent_price = self._get_extra_adult_prices(
            room_name, extra_adult_sku_prices, extra_adult_rack_rate
        )
        price_rule = self._get_price_rule(
            price_aggregate.hotel_id, incremental_input_price, extra_adult_price, subsequent_price
        )
        self._calculate_subsequent_occupancy_prices(
            price_aggregate, price_aggregate.base_room_type_entity, incremental_input_price, extra_adult_price,
            subsequent_price, price_rule,
        )
        room_name_to_room_type = {
            rt_entity.room_type_name.upper(): rt_entity for rt_entity in price_aggregate.room_type_entities
        }
        self._process_incremental_price_rules(
            price_aggregate, incremental_input_price, extra_adult_sku_prices, extra_adult_rack_rate,
            room_name_to_room_type
        )

    def _process_incremental_price_rules(
        self, price_aggregate, incremental_input_price, extra_adult_sku_prices, extra_adult_rack_rate,
        room_name_to_room_type
    ):
        for incremental_price_rule in price_aggregate.applicable_incremental_price_rules:
            sum_factor = incremental_price_rule.sum_factor if incremental_price_rule.sum_factor else DEFAULT_SUM_FACTOR
            incremental_final_price = incremental_input_price * incremental_price_rule.factor + sum_factor
            room_type_name = incremental_price_rule.room_type
            incremental_price_entity = self._create_price_entity(
                price_aggregate, incremental_input_price, incremental_final_price, incremental_price_rule,
                room_type_name, sku_name=f'{room_type_name}-1'
            )
            price_aggregate.add_incremental_room_type_price(incremental_price_entity)

            extra_adult_price, subsequent_price = self._get_extra_adult_prices(
                room_type_name, extra_adult_sku_prices, extra_adult_rack_rate
            )
            room_type = room_name_to_room_type[room_type_name.upper()]
            price_rule = self._get_price_rule(
                price_aggregate.hotel_id, incremental_final_price, extra_adult_price, subsequent_price
            )
            self._calculate_subsequent_occupancy_prices(
                price_aggregate, room_type, incremental_final_price, extra_adult_price, subsequent_price, price_rule
            )

    def _calculate_subsequent_occupancy_prices(
        self, price_aggregate, room_type, input_price, extra_adult_price, subsequent_price, price_rule
    ):
        for occupancy in range(2, room_type.max_adults + 1):
            occupancy_price = self._calculate_occupancy_price(
                input_price, extra_adult_price, subsequent_price, occupancy
            )
            incremental_price_entity = self._create_price_entity(
                price_aggregate, input_price, occupancy_price, price_rule=price_rule,
                room_type=room_type.room_type_name, sku_name=f'{room_type.room_type_name}-{occupancy}'
            )
            price_aggregate.add_incremental_room_type_price(incremental_price_entity, occupancy)

    @staticmethod
    def _calculate_occupancy_price(
        base_price: float, extra_adult_price: float, subsequent_price: float, occupancy: int
    ) -> float:
        total_price = base_price + extra_adult_price
        if occupancy > 2 and subsequent_price:
            total_price += (subsequent_price * (occupancy - 2))
        return total_price

    @staticmethod
    def _create_price_entity(
        price_aggregate, input_price, final_price, price_rule, room_type=None, sku_code=None, sku_name=None,
    ) -> 'RoomTypePrice':
        entity = RoomTypePrice(
            hotel_id=price_aggregate.hotel_id,
            room_type=room_type,
            sku_code=sku_code,
            sku_name=sku_name,
            target_date=price_aggregate.target_date,
            input_price=input_price,
            final_price=final_price,
            rules=[price_rule],
            is_published=False,
            price_trigger_id=price_aggregate.price_trigger_id,
            occupancy_percentage=price_aggregate.hotel_occupancy,
        )
        return entity
