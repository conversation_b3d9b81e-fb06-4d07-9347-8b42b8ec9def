from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.advance_booking_window_handler import (
    AdvanceBookingWindowHandler,
)
from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.base_rule_handler import EndHandler
from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.competitive_price_calculation_handler import (
    CompetitivePriceHandler,
)
from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.percentage_occupancy_evening_handler import (
    PercentageOccupancyEveningHandler,
)
from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.percentage_occupancy_handler import (
    PercentageOccupancyHandler,
)
from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.price_boundaries_handler import (
    PriceBoundariesHandler,
)
from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.target_date_handler import (
    TargetDateHandler,
)
from hawkeye.domain.price_calculation.aggregates.price_aggregate import PriceAggregate
from object_registry import register_instance


@register_instance(
    dependencies=[
        PercentageOccupancyHand<PERSON>,
        AdvanceBooking<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        PercentageOccupancyEvening<PERSON><PERSON><PERSON>,
        PriceBoundariesHandler,
        CompetitivePriceHandler,
    ]
)
class CalculateRuleBasedPriceCommandHandler:
    def __init__(
        self,
        percentage_occupancy_handler: PercentageOccupancyHandler,
        advance_booking_window_handler: AdvanceBookingWindowHandler,
        target_date_handler: TargetDateHandler,
        percentage_occupancy_evening_handler: PercentageOccupancyEveningHandler,
        price_boundaries_handler: PriceBoundariesHandler,
        competitive_price_calculation_handler: CompetitivePriceHandler,
    ):
        self.percentage_occupancy_handler = percentage_occupancy_handler
        self.advance_booking_window_handler = advance_booking_window_handler
        self.target_date_handler = target_date_handler
        self.percentage_occupancy_evening_handler = percentage_occupancy_evening_handler
        self.price_boundaries_handler = price_boundaries_handler
        self.competitive_price_calculation_handler = competitive_price_calculation_handler

    def _setup_handler_chain(self, initial_price_rule):
        chain_rules = [
            self.advance_booking_window_handler,
            self.target_date_handler,
            self.percentage_occupancy_evening_handler,
            self.price_boundaries_handler,
            self.competitive_price_calculation_handler,
        ]
        current = initial_price_rule
        for rule in chain_rules:
            current.next_handler = rule
            current = current.next_handler
        current.next_handler = EndHandler()

    def handle(self, price_aggregate: PriceAggregate):
        initial_price_rule = self.percentage_occupancy_handler
        self._setup_handler_chain(initial_price_rule)

        initial_price_rule.perform_calculation(price_aggregate, price_aggregate.base_room_type_price_rules)
