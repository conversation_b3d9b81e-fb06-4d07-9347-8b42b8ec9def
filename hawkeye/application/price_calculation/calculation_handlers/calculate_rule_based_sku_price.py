from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.advance_booking_window_handler import (
    AdvanceBookingWindowHandler
)
from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.base_rule_handler import EndHandler
from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.percentage_occupancy_evening_handler import (
    PercentageOccupancyEveningHandler
)
from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.percentage_occupancy_handler import (
    PercentageOccupancyHandler
)
from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.price_boundaries_handler import (
    PriceBoundariesHandler
)
from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.target_date_handler import (
    TargetDateHandler
)
from hawkeye.domain.price_calculation.aggregates.sku_price_aggregate import SkuPriceAggregate
from object_registry import register_instance


@register_instance(
    dependencies=[
        PercentageOccupancyHandler,
        AdvanceBookingWindowHandler,
        TargetDateHandler,
        PercentageOccupancy<PERSON><PERSON><PERSON><PERSON><PERSON>,
        PriceBoundariesHandler,
    ]
)
class CalculateRuleBasedSkuPriceCommandHandler:
    def __init__(
        self,
        percentage_occupancy_handler: PercentageOccupancyHandler,
        advance_booking_window_handler: AdvanceBookingWindowHandler,
        target_date_handler: TargetDateHandler,
        percentage_occupancy_evening_handler: PercentageOccupancyEveningHandler,
        price_boundaries_handler: PriceBoundariesHandler,
    ):
        self.percentage_occupancy_handler = percentage_occupancy_handler
        self.advance_booking_window_handler = advance_booking_window_handler
        self.target_date_handler = target_date_handler
        self.percentage_occupancy_evening_handler = percentage_occupancy_evening_handler
        self.price_boundaries_handler = price_boundaries_handler

    def _build_base_handler_chain(self):
        chain = [
            self.percentage_occupancy_handler,
            self.advance_booking_window_handler,
            self.target_date_handler,
            self.percentage_occupancy_evening_handler,
            self.price_boundaries_handler,
            EndHandler()
        ]
        for i in range(len(chain) - 1):
            chain[i].next_handler = chain[i + 1]
        return chain[0]

    def _build_extra_adult_handler_chain(self):
        chain = [
            self.price_boundaries_handler,
            self.target_date_handler,
            EndHandler()
        ]
        for i in range(len(chain) - 1):
            chain[i].next_handler = chain[i + 1]
        return chain[0]

    def handle_extra_adult_skus(self, price_aggregate: SkuPriceAggregate):
        extra_adult_handler_chain = self._build_extra_adult_handler_chain()
        extra_adult_handler_chain.perform_extra_adult_price_calculation(
            price_aggregate, 
            price_aggregate.sku_price_rules
        )
        price_aggregate.finalize_price(price_aggregate.room_type_price_entity)

    def handle(self, price_aggregate, room_type_price):
        base_handler_chain = self._build_base_handler_chain()
        base_handler_chain.perform_calculation_for_skus(
            price_aggregate, 
            price_aggregate.sku_price_rules,
            room_type_price
        )
        price_aggregate.finalize_price(price_aggregate.room_type_price_entity)
