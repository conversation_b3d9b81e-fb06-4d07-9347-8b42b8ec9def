import logging
import json
import os
from collections import defaultdict

import psycopg2
import sentry_sdk
from psycopg2.extras import Dict<PERSON>ursor
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.utils.dateutils import add, current_datetime

from hawkeye.application.decorators import session_manager
from hawkeye.application.services.competitive_price_service import (
    CompetitivePriceService,
)
from hawkeye.common.slack_alert_helpers import SlackAlert
from hawkeye.constants.hawkeye_constant import RATE_PING_DB_SECRET_NAME, RateInfoMealPlanCode
from hawkeye.domain.entities.competitive_hotel_mapping import CompetitiveHotelMapping
from hawkeye.domain.entities.competitor_hotel_pricing import CompetitorHotelPricing
from hawkeye.domain.entities.rate_info import RateInfoEntity
from hawkeye.infrastructure.repositories.competitor_hotel_pricing_repository import (
    CompetitorHotelPricingRepository,
)
from hawkeye.infrastructure.repositories.competitve_hotel_mapping import (
    CompetitiveHotelMappingRepository,
)
from hawkeye.infrastructure.repositories.rate_info_repository import RateInfoRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CompetitorHotelPricingRepository,
        CompetitiveHotelMappingRepository,
        RateInfoRepository,
        CompetitivePriceService,
    ]
)
class RatePingDataSyncService:
    def __init__(
        self,
        competitive_hotel_pricing_repository,
        competitive_hotel_mapping_repository,
        rate_info_repository,
        competitive_price_service,
    ):
        self.competitive_hotel_pricing_repository = competitive_hotel_pricing_repository
        self.competitive_hotel_mapping_repository = competitive_hotel_mapping_repository
        self.rate_info_repository = rate_info_repository
        self.competitive_price_service = competitive_price_service

    @staticmethod
    def _get_db_connection():
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        env = os.environ.get("APP_ENV", "local")
        cluster_identifier = os.environ.get("CLUSTER_IDENTIFIER")
        secret_prefix = os.environ.get("AWS_SECRET_PREFIX")
        secret_name = (
            f"{tenant_id}/{env}/{cluster_identifier}/{secret_prefix}/{RATE_PING_DB_SECRET_NAME}"
        )

        db_creds = AwsSecretManager.get_secret_by_name(secret_name)
        if not db_creds:
            raise Exception("Rate Ping DB creds not found")
        return psycopg2.connect(
            dbname=db_creds["dbname"],
            user=db_creds["user"],
            password=db_creds["password"],
            host=db_creds["host"],
            port=db_creds["port"],
        )

    def sync_data(self, hours=3):
        if hours is None:
            hours = 3
        logger.info("Starting to sync data from rate ping")
        try:
            self.mark_old_rates_inactive()
            logger.info("Marked old rates inactive")
            self.sync_rates_from_rateping(hours)
            logger.info("Synced all rates from rate ping")
            self.sync_hotel_competitor_mappings(hours)
            logger.info("Synced hotel competitor mappings")
            self.generate_competitor_hotel_prices()
            logger.info("Generated competitor hotel prices")
            self.competitive_price_service.calculate_normalized_prices()
            logger.info("Calculated normalized prices")
        except Exception as e:
            sentry_sdk.capture_exception(e)
            logger.info("Failed to sync data from rate ping", exc_info=True)
        logger.info("Finished syncing data from rate ping")

    @session_manager(commit=True)
    def mark_old_rates_inactive(self):
        self.rate_info_repository.mark_all_rates_inactive()

    def sync_rates_from_rateping(self, hours=3):
        conn = self._get_db_connection()
        cursor = conn.cursor(name='streaming_cursor', cursor_factory=DictCursor)
        cursor.execute(
            "SELECT * FROM rate_info WHERE insert_time > NOW() - INTERVAL '%s hour';",
            (hours,)
        )
        count = 0
        while True:
            rows = cursor.fetchmany(1000)
            if not rows:
                break
            rates = [RateInfoEntity(**row) for row in rows]
            self.save_rates(rates)
            count += len(rates)
            logger.info(f"Synced {count} rates from rate ping")

    @session_manager(commit=True)
    def save_rates(self, rates):
        self.rate_info_repository.create_all(rates)

    def fetch_competitor_hotel_mappings(self, hours=3):
        conn = self._get_db_connection()
        cursor = conn.cursor(cursor_factory=DictCursor)
        cursor.execute(
            "SELECT treebo_cs_id, mapped_hotel_ids FROM treebo_hotel_mapping "
            "WHERE created_at > NOW() - INTERVAL '%s hour' OR updated_at > NOW() - INTERVAL '%s hour';",
            (hours, hours)
        )
        treebo_cs_id_to_competitive_ids = defaultdict(list)
        while True:
            rows = cursor.fetchmany(1000)
            if not rows:
                break
            for row in rows:
                try:
                    mapped_hotel_ids = json.loads(row[1])
                except Exception as e:
                    sentry_sdk.capture_exception(e)
                    mapped_hotel_ids = []
                treebo_cs_id = str(row[0]).zfill(7)
                if treebo_cs_id_to_competitive_ids[treebo_cs_id]:
                    SlackAlert.send_alert(
                        f"Duplicate entry found for Treebo cs id: {treebo_cs_id}."
                    )
                    continue
                treebo_cs_id_to_competitive_ids[treebo_cs_id] = mapped_hotel_ids

        return treebo_cs_id_to_competitive_ids

    @session_manager(commit=True)
    def update_or_create_hotel_mappings(self, internal_hotel_id, new_mapped_ids):
        current_mappings = self.competitive_hotel_mapping_repository.load_all_mappings(
            internal_hotel_id
        )
        current_mappings_to_hotel_id = {
            mapping.competitive_hotel_id: mapping for mapping in current_mappings
        }
        new_hotel_mappings = []
        for competitive_hotel_id in new_mapped_ids:
            if competitive_hotel_id not in current_mappings_to_hotel_id:
                new_mapping = CompetitiveHotelMapping(
                    internal_hotel_id=internal_hotel_id,
                    competitive_hotel_id=competitive_hotel_id,
                    is_active=True,
                )
                new_hotel_mappings.append(new_mapping)
        self.competitive_hotel_mapping_repository.create_all(new_hotel_mappings)

    def sync_hotel_competitor_mappings(self, hours=3):
        competitor_hotel_mappings = self.fetch_competitor_hotel_mappings(hours)
        for internal_hotel_id, new_mapped_ids in competitor_hotel_mappings.items():
            try:
                self.update_or_create_hotel_mappings(internal_hotel_id, new_mapped_ids)
            except Exception as e:
                sentry_sdk.capture_exception(e)

    def generate_competitor_hotel_prices(self):
        unique_hotel_ids = self.rate_info_repository.load_unique_hotel_ids()
        logger.info(f"Generating competitor hotel prices for {unique_hotel_ids}")
        for hotel_id in unique_hotel_ids:
            logger.info(f"Generating competitor hotel prices for hotel id: {hotel_id}")
            hotel_active_rates = self.rate_info_repository.load_all_active_rates_of_hotel(hotel_id)
            grouped_rates = defaultdict(list)
            for rate in hotel_active_rates:
                grouped_rates[rate.check_in].append(rate)
            for check_in, rates in grouped_rates.items():
                self.handle_new_rates(str(hotel_id), check_in, rates)

    @session_manager(commit=True)
    def handle_new_rates(self, hotel_id, stay_date, rates):
        # TODO: Improve this logic for all of the stay dates of a hotel in a single query
        room_wise_last_pull_rates = defaultdict(lambda: None)
        for rate in rates:
            if rate.meal_plan_code != RateInfoMealPlanCode.CP.value:
                continue
            if room_wise_last_pull_rates.get(rate.room_name):
                previous_rate = room_wise_last_pull_rates[rate.room_name]
                if previous_rate.insert_time < rate.insert_time:
                    room_wise_last_pull_rates[rate.room_name] = rate
                elif previous_rate.insert_time == rate.insert_time:
                    room_wise_last_pull_rates[rate.room_name] = (
                        rate if previous_rate.on_site_rate >= rate.on_site_rate else previous_rate
                    )
            else:
                room_wise_last_pull_rates[rate.room_name] = rate

        new_competitor_hotel_prices = []
        for room_name, rate in room_wise_last_pull_rates.items():
            new_competitor_hotel_prices.append(
                CompetitorHotelPricing(
                    competitor_hotel_id=hotel_id,
                    stay_date=stay_date,
                    price=rate.on_site_rate,
                    room_type=room_name[:500],
                    expires_at=add(current_datetime(), days=2),
                )
            )

        existing_competitor_prices = (
            self.competitive_hotel_pricing_repository.load_competitor_prices(
                [hotel_id], [stay_date]
            )
        )
        expired_competitor_prices = []
        for competitor_price in existing_competitor_prices:
            if room_wise_last_pull_rates.get(competitor_price.room_type):
                competitor_price.expires_at = current_datetime()
                expired_competitor_prices.append(competitor_price)

        if expired_competitor_prices:
            self.competitive_hotel_pricing_repository.update_all(expired_competitor_prices)
        self.competitive_hotel_pricing_repository.create_all(new_competitor_hotel_prices)
