import logging
import traceback
from collections import defaultdict
from datetime import datetime

from hawkeye.application.decorators import request_middleware
from hawkeye.application.decorators import session_manager
from hawkeye.infrastructure.telemetry.decorators import background_task
from hawkeye.constants.hawkeye_constant import PricePushEventStatus, PRICE_PUSH_COMBINED_DATES
from hawkeye.domain.factory.price_push_event_factory import PricePushEventFactory
from hawkeye.infrastructure.publisher.price_publisher import PricePublisher
from hawkeye.infrastructure.repositories.room_type_price_repository import (
    RoomTypePriceRepository
)
from hawkeye.utils.collectionutils import chunks
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[PricePublisher, RoomTypePriceRepository])
class PricePushEventService(object):

    def __init__(self, price_publisher, room_type_price_repository):
        self.price_publisher = price_publisher
        self.room_type_price_repository = room_type_price_repository

    @request_middleware
    @background_task(name='price_push_worker')
    def publish_unpublished_prices_to_queue(self):
        logger.info('Checking for unpublished hotel target dates')
        hotel_target_dates = self._get_unpublished_hotel_target_dates()
        hotel_wise_target_dates = defaultdict(list)
        for hotel_target_date_tuple in hotel_target_dates:
            hotel_wise_target_dates[hotel_target_date_tuple[0]].append(hotel_target_date_tuple[1])

        for hotel_id, target_dates in hotel_wise_target_dates.items():
            try:
                logger.info("Publishing prices to queue")
                self.publish_prices_to_queue(hotel_id, target_dates)
            except Exception as e:
                logger.error(f"Price Push error for {hotel_id}, dates {target_dates}")
                logger.error(e, exc_info=True)

    @session_manager(commit=True)
    def publish_prices_to_queue(self, hotel_id, target_dates):
        room_type_prices = self.room_type_price_repository.load_bulk_for_update(hotel_id, target_dates)
        date_wise_price_push_events = dict()
        try:
            for room_type_price in room_type_prices:
                room_type_price.mark_published()
                target_date = room_type_price.target_date
                if room_type_price.sku_code is None:
                    continue
                if target_date in date_wise_price_push_events:
                    date_wise_price_push_events[target_date].add_price_to_price_push_event_payload(room_type_price)
                else:
                    date_wise_price_push_events[target_date] = PricePushEventFactory.create_price_push_event_payload(
                        room_type_price
                    )
            self.extract_price_push_payload_and_publish(date_wise_price_push_events)
            self.room_type_price_repository.bulk_update_mappings(room_type_prices)
        except Exception as e:
            logger.exception(traceback.format_exc())
            logger.info(
                "PRICE PUSH TO RACKRATE started at: %s and ended at: %s ran with status: %s ",
                datetime.now(),
                datetime.now(),
                PricePushEventStatus.FAILED,
            )
            raise

    def extract_price_push_payload_and_publish(self, date_wise_price_push_events):
        price_push_events = []
        for price_push_event in date_wise_price_push_events.values():
            price_push_events.append(price_push_event.dict())
        if not price_push_events:
            logger.info("No updated price to push in RACKRATE")
            return
        for price_push_event_chunks in chunks(price_push_events, PRICE_PUSH_COMBINED_DATES):
            self.price_publisher.publish(price_push_event_chunks)

    @session_manager()
    def _get_unpublished_hotel_target_dates(self):
        return self.room_type_price_repository.get_unpublished_hotel_target_dates()
