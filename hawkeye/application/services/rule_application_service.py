import logging
from collections import defaultdict

import sentry_sdk
import time

from treebo_commons.utils import dateutils

from hawkeye.application.decorators import session_manager
from hawkeye.application.services.dtos.pricing_rule_dtos import PricingRuleDto
from hawkeye.constants.hawkeye_constant import RuleType
from hawkeye.domain.factory.base_room_type_price_rule_factory import BaseRoomTypePriceRuleFactory
from hawkeye.domain.factory.base_room_type_sku_price_rule_factory import BaseRoomTypeSkuPriceRuleFactory
from hawkeye.domain.factory.rule_file_upload_factory import RuleFileUploadFactory
from hawkeye.infrastructure.repositories.base_room_type_price_rule_repository import (
    BaseRoomTypePriceRuleRepository,
)
from hawkeye.infrastructure.repositories.base_room_type_sku_price_rule_repository import (
    BaseRoomTypeSkuPriceRuleRepository
)
from hawkeye.infrastructure.repositories.rule_file_repository import RuleFileRepository
from hawkeye.utils.collectionutils import chunks
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[
    RuleFileRepository,
    BaseRoomTypePriceRuleRepository,
    BaseRoomTypeSkuPriceRuleRepository,
])
class RuleApplicationService:
    def __init__(
        self,
        rule_file_repository: RuleFileRepository,
        base_room_type_price_rule_repository: BaseRoomTypePriceRuleRepository,
        base_room_type_sku_price_rule_repository: BaseRoomTypeSkuPriceRuleRepository,
    ):
        self.rule_file_repository = rule_file_repository
        self.base_room_type_price_rule_repository = base_room_type_price_rule_repository
        self.base_room_type_sku_price_rule_repository = base_room_type_sku_price_rule_repository

    @session_manager(commit=True)
    def save_rule_file(self, rule_file_dto):
        rule_file = RuleFileUploadFactory.create_rule_file_upload_config(rule_file_dto)
        logger.info("Saving Upload Rule file, status marked as pending")
        file_id = self.rule_file_repository.save(rule_file_upload=rule_file)
        return file_id

    def _process_and_save_file_rules(self, rule_file, rule_file_data):
        logger.info(f"Start fetching hotel rule type mapping, time: {time.time()}")
        hotel_ids = set()
        pricing_rule_dtos = []
        for data in rule_file_data:
            hotel_ids.add(data['hotel_id'])
            data['file_id'] = rule_file.file_id
            dto = PricingRuleDto.create_from_data(data=data)
            if dto:
                pricing_rule_dtos.append(dto)
        rules = self.base_room_type_price_rule_repository.load_all(hotel_ids=list(hotel_ids))
        hotel_wise_rule_type_mapping = defaultdict(lambda: defaultdict(list))
        for rule in rules:
            hotel_wise_rule_type_mapping[rule.hotel_id][rule.rule_type].append(rule)
        update_pricing_rule_dtos = set()
        update_pricing_rule_target_date_dtos = set()
        for dto in pricing_rule_dtos:
            if dto.rule_type in hotel_wise_rule_type_mapping.get(dto.hotel_id, dict()):
                if dto.rule_type == RuleType.TARGET_DATE.value:
                    update_pricing_rule_target_date_dtos.add((dto.rule_type, dto.hotel_id,
                                                              dto.config.to_json()[
                                                                  'target_date']))
                else:
                    update_pricing_rule_dtos.add((dto.rule_type, dto.hotel_id))

        logger.info(f"Start creating list of pricing rule objects, time: {time.time()}")
        create_pricing_rules = [
            BaseRoomTypePriceRuleFactory.create_pricing_rule(dto)
            for dto in pricing_rule_dtos
        ]

        update_pricing_rules = []
        logger.info(f"Going inside update_pricing_rule_target_date_dtos loop, time: {time.time()}")
        for dto in update_pricing_rule_target_date_dtos:
            rule_type, hotel_id, target_date = dto[0], dto[1], dto[2]
            logger.info(f"fetching pricing rule for hotel {hotel_id} and rule type {rule_type}, time: {time.time()}")
            pricing_rules = hotel_wise_rule_type_mapping[hotel_id][rule_type]
            for pricing_rule in pricing_rules:
                if dateutils.date_to_ymd_str(pricing_rule.config.target_date) == target_date:
                    pricing_rule.mark_delete()
                    update_pricing_rules.append(pricing_rule)
        logger.info(f"Finished update_pricing_rule_target_date_dtos loop, time: {time.time()}")

        logger.info(f"Going inside update_pricing_rule_dtos loop, time: {time.time()}")
        for dto in update_pricing_rule_dtos:
            rule_type, hotel_id = dto[0], dto[1]
            logger.info(f"fetching pricing rule for hotel {hotel_id} and rule type {rule_type}, time: {time.time()}")
            pricing_rules = hotel_wise_rule_type_mapping[hotel_id][rule_type]
            for pricing_rule in pricing_rules:
                pricing_rule.mark_delete()
                update_pricing_rules.append(pricing_rule)
        logger.info(f"Finished update_pricing_rule_dtos loop, time: {time.time()}")

        logger.info(f"Saving rules , time: {time.time()}")
        self.save_rules(self.base_room_type_price_rule_repository, create_pricing_rules, update_pricing_rules)
        logger.info("Processing Rule file successful, status marked as success")
        rule_file.mark_success()

    @session_manager(commit=True)
    def process_rule_file(self, file_id, rule_file_data, is_sku_rule_file=False):
        logger.info(f"Loading rule file (id={file_id}), time: {time.time()}")
        rule_file = self.rule_file_repository.load_for_update(file_id)
        if rule_file_data:
            try:
                logger.info(f"Starting to process rule file data, time: {time.time()}")
                if is_sku_rule_file:
                    self._process_and_save_sku_file_rules(rule_file, rule_file_data)
                else:
                    self._process_and_save_file_rules(rule_file, rule_file_data)
            except Exception as ex:
                logger.info("Processing Rule file failed, status marked as failed")
                rule_file.mark_failed()
                sentry_sdk.capture_exception(ex)
        else:
            logger.error("Either Rule file is empty or all the rules are invalid")
            rule_file.mark_failed()
        self.rule_file_repository.update_rule_file(rule_file)

    @staticmethod
    def save_rules(repository, create_pricing_rules, update_pricing_rules):
        for chunk in chunks(create_pricing_rules, 50):
            repository.create_all(chunk)
        for chunk in chunks(update_pricing_rules, 50):
            repository.update_all(chunk)

    def _process_and_save_sku_file_rules(self, rule_file, rule_file_data):
        sku_pricing_rule_dtos, hotel_ids = [], set()
        for data in rule_file_data:
            hotel_ids.add(data['hotel_id'])
            data['file_id'] = rule_file.file_id
            dto = PricingRuleDto.create_from_data(data)
            sku_pricing_rule_dtos.append(dto)
        rules = self.base_room_type_sku_price_rule_repository.load_all(hotel_ids=list(hotel_ids))
        hotel_wise_sku_rule_mapping = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
        for rule in rules:
            hotel_wise_sku_rule_mapping[rule.hotel_id][rule.sku_code][rule.rule_type].append(rule)

        update_sku_pricing_rule_dtos = set()
        update_sku_pricing_rule_target_date_dtos = set()
        for dto in sku_pricing_rule_dtos:
            if dto.rule_type in hotel_wise_sku_rule_mapping.get(dto.hotel_id, dict()).get(dto.sku_code, set()):
                if dto.rule_type == RuleType.TARGET_DATE.value:
                    update_sku_pricing_rule_target_date_dtos.add((dto.rule_type, dto.hotel_id, dto.sku_code,
                                                                  dto.config.to_json()[
                                                                      'target_date']))
                else:
                    update_sku_pricing_rule_dtos.add((dto.rule_type, dto.hotel_id, dto.sku_code))

        create_sku_pricing_rules = [
            BaseRoomTypeSkuPriceRuleFactory.create_sku_pricing_rule(dto)
            for dto in sku_pricing_rule_dtos
        ]

        update_sku_pricing_rules = []
        logger.info(f"Going inside update_pricing_rule_target_date_dtos loop, time: {time.time()}")
        for dto in update_sku_pricing_rule_target_date_dtos:
            rule_type, hotel_id, sku_code, target_date = dto[0], dto[1], dto[2], dto[3]
            logger.info(f"fetching pricing rule for hotel {hotel_id} and rule type {rule_type}, time: {time.time()}")
            pricing_rules = hotel_wise_sku_rule_mapping[hotel_id][sku_code][rule_type]
            for pricing_rule in pricing_rules:
                if dateutils.date_to_ymd_str(pricing_rule.config.target_date) == target_date:
                    pricing_rule.mark_delete()
                    update_sku_pricing_rules.append(pricing_rule)

        for dto in update_sku_pricing_rule_dtos:
            rule_type, hotel_id, sku_code = dto[0], dto[1], dto[2]
            logger.info(f"fetching pricing rule for hotel {hotel_id} and rule type {rule_type}, time: {time.time()}")
            pricing_rules = hotel_wise_sku_rule_mapping[hotel_id][sku_code][rule_type]
            for pricing_rule in pricing_rules:
                pricing_rule.mark_delete()
                update_sku_pricing_rules.append(pricing_rule)

        self.save_rules(
            self.base_room_type_sku_price_rule_repository, create_sku_pricing_rules, update_sku_pricing_rules
        )
        rule_file.mark_success()
