import logging
from typing import List

from hawkeye.application.decorators import request_middleware, session_manager
from hawkeye.application.services.dtos.catalog_dtos import (
    CatalogHotelDto,
    HotelRoomTypeDto,
)
from hawkeye.constants.hawkeye_constant import (
    HotelStatus,
    SKU_STAY_CATEGORY_CODE,
    SKU_CODE_CACHE_EXPIRY,
)
from hawkeye.domain.factory.hotel_factory import (
    HotelFactory,
    HotelSkuFactory,
    RoomTypeFactory,
)
from hawkeye.infrastructure.cache import cache
from hawkeye.infrastructure.cache.cache_key import KeyConvertor
from hawkeye.infrastructure.external_clients.catalog_client import CatalogServiceClient
from hawkeye.infrastructure.repositories.hotel_config_repository import (
    HotelConfigRepository,
)
from hawkeye.infrastructure.repositories.hotel_room_type_repository import (
    HotelRoomTypeRepository,
)
from hawkeye.infrastructure.repositories.hotel_sku_repository import HotelSkuRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        HotelConfigRepository,
        HotelRoomTypeRepository,
        CatalogServiceClient,
        HotelSkuRepository,
    ]
)
class CatalogService(object):
    def __init__(
        self,
        hotel_config_repository,
        room_type_repository,
        catalog_service_client,
        hotel_sku_repository,
    ):
        self.hotel_config_repository = hotel_config_repository
        self.hotel_room_type_repository = room_type_repository
        self.hotel_sku_repository = hotel_sku_repository
        self.catalog_service_client = catalog_service_client

    @session_manager(commit=True)
    def add_hotel(self, hotel_dto: CatalogHotelDto):
        hotel_entity = HotelFactory.create_hotel_config(hotel_dto)
        self.hotel_config_repository.create(hotel_entity)
        self.add_hotel_skus(hotel_dto)
        return hotel_entity

    def add_hotel_skus(self, hotel_dto: CatalogHotelDto):
        hotel_skus = HotelSkuFactory.create_from_hotel_skus_dtos(hotel_dto.hotel_skus)
        self.hotel_sku_repository.create_all(hotel_skus)

    @session_manager(commit=True)
    def update_hotel(self, hotel_dto: CatalogHotelDto):
        hotel_config_entity = self.hotel_config_repository.load_for_update(hotel_dto.hotel_id)
        hotel_config_entity.update_status_from_dto(hotel_dto)
        self.hotel_config_repository.update(hotel_config_entity)
        self.update_or_create_hotel_skus(hotel_dto)
        return hotel_config_entity

    def update_or_create_hotel_skus(self, hotel_dto: CatalogHotelDto):
        new_hotel_skus = HotelSkuFactory.create_from_hotel_skus_dtos(hotel_dto.hotel_skus)
        existing_hotel_skus = self.hotel_sku_repository.load_all_hotel_skus(
            hotel_dto.hotel_id
        )
        new_hotel_sku_codes = {sku.sku_code for sku in new_hotel_skus}
        existing_sku_codes = {sku.sku_code for sku in existing_hotel_skus}

        skus_to_update = new_hotel_sku_codes & existing_sku_codes
        skus_to_delete = existing_sku_codes - new_hotel_sku_codes

        new_skus_to_sku_codes = {sku.sku_code: sku for sku in new_hotel_skus}
        existing_skus_to_sku_codes = {sku.sku_code: sku for sku in existing_hotel_skus}

        for sku_code in skus_to_update:
            existing_skus_to_sku_codes[sku_code].update(new_skus_to_sku_codes[sku_code])

        for sku_code in skus_to_delete:
            existing_skus_to_sku_codes[sku_code].mark_inactive()

        sku_codes_to_create = new_hotel_sku_codes - existing_sku_codes
        skus_to_be_created = [
            sku
            for sku in new_hotel_skus
            if sku.sku_code in sku_codes_to_create
        ]

        self.hotel_sku_repository.update_all(existing_hotel_skus)
        self.hotel_sku_repository.create_all(skus_to_be_created)

    @session_manager(commit=True)
    def add_room_type_config(self, room_type_config_dto: HotelRoomTypeDto):
        hotel_room_type_entity = RoomTypeFactory.create_room_type_config(room_type_config_dto)
        self.hotel_room_type_repository.create(hotel_room_type_entity)
        return hotel_room_type_entity

    @session_manager(commit=True)
    def update_room_type_config(self, room_type_config_dto: HotelRoomTypeDto):
        hotel_room_type_entity = self.hotel_room_type_repository.load_for_update(room_type_config_dto.hotel_id,
                                                                                 room_type_config_dto.room_type_code)
        hotel_room_type_entity.update_room_config(room_type_config_dto)
        self.hotel_room_type_repository.update(hotel_room_type_entity)
        return hotel_room_type_entity

    @request_middleware
    @session_manager(commit=True)
    def save_hotels_from_catalog(self, hotel_ids):
        hotel_configs = []
        hotel_room_type_configs = []
        hotel_skus = []
        for hotel_id in hotel_ids:
            hotel_config, hotel_room_type_config, hotel_skus = self._load_hotel_from_catalog(hotel_id=hotel_id)
            hotel_configs.append(hotel_config)
            hotel_room_type_configs.extend(hotel_room_type_config)
        self.hotel_config_repository.create_all(hotel_configs)
        self.hotel_room_type_repository.create_all(hotel_room_type_configs)
        self.hotel_sku_repository.create_all(hotel_skus)

    def _load_hotel_from_catalog(self, hotel_id):
        catalog_hotel_response = self.catalog_service_client.fetch_hotel(hotel_id)
        hotel_dto = CatalogHotelDto.create_from_catalog_data(catalog_hotel_response)
        hotel_room_type_config_dtos = []
        for room_type_config in catalog_hotel_response["room_type_configs"]:
            hotel_room_type_config_dto = (
                HotelRoomTypeDto.create_from_catalog_data(
                    hotel_id, room_type_config
                )
            )
            hotel_room_type_config_dtos.append(hotel_room_type_config_dto)
        return (
            HotelFactory.create_hotel_config(hotel_dto),
            RoomTypeFactory.create_room_type_configs(hotel_room_type_config_dtos),
            HotelSkuFactory.create_from_hotel_skus_dtos(hotel_dto.hotel_skus),
        )

    @request_middleware
    def get_eligible_hotels_for_trigger_migration(self, hotel_ids):
        """
        return eligible hotel for trigger migration
        if hotel already exist in hawkeye migration won't run for that hotel
        """
        hotel_config_ids = set([hotel_config.hotel_id for hotel_config in self.hotel_config_repository.load_all()])
        if hotel_ids:
            return [hotel_id for hotel_id in hotel_ids if hotel_id not in hotel_config_ids]
        else:
            return [hotel_data["id"] for hotel_data in self.catalog_service_client.get_all_hotels()
                    if hotel_data["status"] != HotelStatus.CHURNED.value and hotel_data["id"] not in hotel_config_ids]

    def get_hotel_ids(self):
        return [hotel_config.hotel_id for hotel_config in self.hotel_config_repository.load_all()]

    def does_hotel_exist(self, hotel_id):
        hotel_config_model = self.hotel_config_repository.load(hotel_id)
        return True if hotel_config_model else False

    @session_manager(commit=True)
    def update_rack_rate(self, room_rack_rate_dto):
        """
        set rackrate in DB which later can be used to calculate pricing if rules are not present
        """
        if room_rack_rate_dto.adult_count != 1:
            return
        hotel_room_type = self.hotel_room_type_repository.load_for_update(room_rack_rate_dto.hotel_id,
                                                                          room_rack_rate_dto.room_type)
        hotel_room_type.update_rack_rate(room_rack_rate_dto.rack_rate)
        self.hotel_room_type_repository.update(hotel_room_type)

    def get_non_base_room_types_for_hotel(self, hotel_id: str):
        room_type_models = self.hotel_room_type_repository.load_hotel_room_types(
            hotel_id=hotel_id, exclude_base_room_type=True)
        return [room_type.room_type_name for room_type in room_type_models]

    def fetch_and_cache_skus(self):
        skus = self.catalog_service_client.get_skus()
        sku_name_to_sku_code = {}
        for sku in skus:
            if sku['sku_category_code'] != SKU_STAY_CATEGORY_CODE:
                continue
            sku_code_key = KeyConvertor.make_sku_code_key(sku['name'])
            sku_name_to_sku_code[sku_code_key] = sku['code']
        cache.mset_to_cache(sku_name_to_sku_code, ttl=SKU_CODE_CACHE_EXPIRY)
        return sku_name_to_sku_code

    def get_active_room_types(self, hotel_id: str):
        return [room_type for room_type in self.hotel_room_type_repository.load_hotel_room_types(hotel_id)]

    def get_catalog_hotel(self, hotel_id):
        catalog_hotel_response = self.catalog_service_client.fetch_hotel(hotel_id)
        dto = CatalogHotelDto.create_from_catalog_data(catalog_hotel_response)
        return dto
