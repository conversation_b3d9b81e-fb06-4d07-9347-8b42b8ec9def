from collections import defaultdict
import logging
from typing import List
from dateutil.parser import parse

from hawkeye.application.decorators import session_manager
from hawkeye.application.services.dtos.catalog_dtos import RoomInformationDto
from hawkeye.application.services.dtos.inventory_dtos import RoomTypeInventoryDto
from hawkeye.domain.factory.hotel_inventory_factory import HotelInventoryFactory
from hawkeye.infrastructure.exception import DatabaseError, CrsClientException
from hawkeye.infrastructure.external_clients.catalog_client import CatalogServiceClient
from hawkeye.infrastructure.external_clients.crs_service_client import CrsClient
from hawkeye.infrastructure.repositories.hotel_config_repository import HotelConfigRepository
from hawkeye.infrastructure.repositories.hotel_inventory_repository import HotelRoomTypeInventoryRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CrsClient,
        HotelConfigRepository,
        HotelRoomTypeInventoryRepository,
        CatalogServiceClient,
    ]
)
class InventoryService(object):
    def __init__(
        self,
        crs_client,
        hotel_config_repository,
        hotel_inventory_repository,
        catalog_service_client,
    ):
        self.crs_client = crs_client
        self.hotel_config_repository = hotel_config_repository
        self.hotel_inventory_repository = hotel_inventory_repository
        self.catalog_service_client = catalog_service_client

    @session_manager(commit=True)
    def save_inventory_from_crs(self, hotel_ids, from_date, to_date):
        hotel_room_inventories = []
        for hotel_id in hotel_ids:
            try:
                hotel_room_inventories.extend(self.load_hotel_inventory_from_crs(hotel_id, from_date, to_date))
            except CrsClientException as ex:
                logger.error(f"Skipped inventory sync for hotel id:{hotel_id}, Exception: {ex}")
        self.hotel_inventory_repository.save_all(hotel_room_inventories)

    def load_hotel_inventory_from_crs(self, hotel_id, from_date, to_date):
        hotel_dnr_information = self.crs_client.get_dnr_information(hotel_id, from_date, to_date)
        room_type_inventories = self.crs_client.get_room_type_inventories(hotel_id, from_date, to_date)
        # TODO remove dependency of catalog client to get room_mapping (required in dnr calculation)
        hotel_room_information = RoomInformationDto.create_from_catalog_data(
            self.catalog_service_client.get_rooms_information(hotel_id)
        )
        room_type_inventory_dtos = RoomTypeInventoryDto.create_from_crs_and_dnr_data(
            hotel_id,
            room_type_inventories=room_type_inventories,
            hotel_dnr_information=hotel_dnr_information,
            room_id_mapping=hotel_room_information.room_id_mapping,
        )
        return [HotelInventoryFactory.create_hotel_inventory(room_type_inventory_dto) for
                room_type_inventory_dto in room_type_inventory_dtos]

    def sync_inventory_from_crs(self, hotel_ids, from_date, to_date):
        if not hotel_ids:
            hotel_ids = [hotel_config.hotel_id for hotel_config in self.hotel_config_repository.load_all_enabled()]
        for hotel_id in hotel_ids:
            hotel_room_inventories = self.load_hotel_inventory_from_crs(hotel_id, from_date, to_date)
            self.create_or_update_inventory(hotel_id, from_date, to_date, hotel_room_inventories)

    @session_manager(commit=True)
    def create_or_update_inventory(self, hotel_id, from_date, to_date, hotel_room_inventories):
        from_date = parse(from_date).date()
        to_date = parse(to_date).date()
        existing_hotel_room_inventories = self.hotel_inventory_repository.load_hotel_room_inventories(hotel_id, from_date, to_date)
        room_type_date_to_inventory = defaultdict()
        for inventory in existing_hotel_room_inventories:
            room_type_date_to_inventory[(inventory.room_type, inventory.date.isoformat())] = inventory
        new_inventories = []
        updated_inventories = []
        for inventory in hotel_room_inventories:
            existing_inventory = room_type_date_to_inventory.get((inventory.room_type, inventory.date))
            if (
                existing_inventory
                and (
                    existing_inventory.out_of_order != inventory.out_of_order
                    or existing_inventory.out_of_service != inventory.out_of_service
                    or existing_inventory.availability_count != inventory.availability_count
                )
            ):
                updated_inventories.append(inventory)
            elif not existing_inventory:
                new_inventories.append(inventory)
        if new_inventories:
            self.hotel_inventory_repository.save_all(new_inventories)
        if updated_inventories:
            self.hotel_inventory_repository.update_all(updated_inventories)

    @session_manager(commit=True)
    def save_room_type_inventories(self, room_type_inventory_dtos: List[RoomTypeInventoryDto]):
        room_type_inventories_to_create = list()
        room_type_inventories_to_update = list()
        for room_type_inventory_dto in room_type_inventory_dtos:
            try:
                room_type_inventory = self.hotel_inventory_repository.load_for_update(
                    hotel_id=room_type_inventory_dto.hotel_id,
                    room_type=room_type_inventory_dto.room_type,
                    date=room_type_inventory_dto.date,
                )
                room_type_inventory.update_availability_count(
                    room_type_inventory_dto.availability_count
                )
                if room_type_inventory_dto.should_increment_dnr is not None:
                    room_type_inventory.update_dnr_count(
                        room_type_inventory_dto.should_increment_dnr
                    )
                room_type_inventories_to_update.append(room_type_inventory)
            except DatabaseError:
                room_type_inventory = HotelInventoryFactory.create_hotel_inventory(room_type_inventory_dto)
                room_type_inventories_to_create.append(room_type_inventory)

        self.hotel_inventory_repository.save_all(room_type_inventories_to_create)
        self.hotel_inventory_repository.update_all(room_type_inventories_to_update)
