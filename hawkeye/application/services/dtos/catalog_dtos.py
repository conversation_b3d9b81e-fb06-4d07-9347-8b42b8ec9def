from typing import Any, Dict, List, Optional

from pydantic import BaseModel
from sqlalchemy import false

from hawkeye.constants.hawkeye_constant import SKU_STAY_CATEGORY_CODE, HotelStatus, SkuNameComponent


class HotelRoomTypeDto(BaseModel):
    hotel_id: str
    room_type_code: str
    room_type_name: str
    total_rooms: int
    max_adults: int
    max_children: int

    @staticmethod
    def create_from_catalog_data(hotel_id, catalog_room_type_config_data):
        return HotelRoomTypeDto(
            hotel_id=hotel_id,
            room_type_code=catalog_room_type_config_data["room_type"]["code"],
            room_type_name=catalog_room_type_config_data["room_type"]["type"],
            total_rooms=catalog_room_type_config_data["room_count"],
            max_adults=catalog_room_type_config_data["adults"],
            max_children=catalog_room_type_config_data["children"],
        )


class PropertySku(BaseModel):
    status: Optional[str] = None
    code: Optional[str] = None
    extra_information: Optional[Dict[str, Any]] = None
    sku_category: Optional[str] = None
    is_active: Optional[bool] = False
    rack_rate: Optional[float] = None
    description: Optional[str] = None
    sku_name: Optional[str] = None
    hotel_id: Optional[str] = None


class CatalogHotelDto(BaseModel):
    hotel_id: str
    status: HotelStatus
    hotel_skus: List[PropertySku] = []
    hotel_name: Optional[str] = None
    hotel_city: Optional[str] = None

    @staticmethod
    def create_from_catalog_data(data):
        def is_dynamic_pricing_applicable(sku):
            if not sku.get("sku_name"):
                return False
            sku_name: str = sku["sku_name"]
            if sku_name.startswith(SkuNameComponent.ECI) or sku_name.startswith(SkuNameComponent.LCO):
                return True
            elif SkuNameComponent.EXTRA_ADULT in sku_name:
                return True
            return False

        hotel_name = data.get("name", {}).get("new_name")
        hotel_city = data.get("location", {}).get("city", {}).get("name")
        hotels_skus = []
        hotel_skus_data = data.get("skus")
        if hotel_skus_data:
            for sku_data in hotel_skus_data:
                if not (sku_data.get("saleable") and is_dynamic_pricing_applicable(sku_data)):
                    continue
                hotels_skus.append(
                    PropertySku(
                        code=sku_data.get("sku_code"),
                        sku_name=sku_data.get("sku_name"),
                        rack_rate=float(sku_data["rack_rate"]) if sku_data.get("rack_rate") else 0,
                        extra_information=sku_data.get("extra_information"),
                        description=sku_data.get("description"),
                        sku_category=sku_data.get("sku_category_code"),
                        is_active=sku_data.get("saleable"),
                        hotel_id=data["id"],
                    )
                )

        return CatalogHotelDto(hotel_id=data["id"], status=data["status"], hotel_skus=hotels_skus,
                               hotel_name=hotel_name, hotel_city=hotel_city)


class RoomInformationDto(BaseModel):
    room_id_mapping = dict()

    @staticmethod
    def create_from_catalog_data(hotel_room_informations):
        room_id_mapping = dict()
        for room_info in hotel_room_informations:
            room_id_mapping[room_info["id"]] = room_info["room_type"]["code"]
        return RoomInformationDto(room_id_mapping=room_id_mapping)


class RoomRackRateDto(object):
    def __init__(self, hotel_id, room_type, adult_count, rack_rate):
        self.hotel_id = hotel_id
        self.room_type = room_type
        self.adult_count = adult_count
        self.rack_rate = rack_rate

    @staticmethod
    def create_from_catalog_data(room_rack_rate_data):
        return RoomRackRateDto(hotel_id=room_rack_rate_data.get('property_id'),
                               room_type=room_rack_rate_data.get('room_type'),
                               adult_count=room_rack_rate_data['adult_count'],
                               rack_rate=room_rack_rate_data['rack_rate'])
