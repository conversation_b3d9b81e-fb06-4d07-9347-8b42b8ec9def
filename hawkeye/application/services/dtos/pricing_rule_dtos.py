import logging
from datetime import datetime
from decimal import Decimal
from typing import Optional

from pydantic import BaseModel

from hawkeye.constants.hawkeye_constant import RuleType
from hawkeye.domain.value_objects.rule_config_detail import (
    RuleConfigDetail,
    OccupancyRangeRuleConfigDetail, ABWRuleConfigDetail, TargetDateRuleConfigDetail,
    SameDayOccupancyRuleConfigDetail,
    ExtraAdultOccupancyRuleConfigDetail,
)

logger = logging.getLogger(__name__)


class PricingRuleDto(BaseModel):
    hotel_id: str
    rule_name: str
    rule_type: str
    config: Optional[RuleConfigDetail]
    is_input_price: bool
    factor: Optional[Decimal]
    sum_factor: Optional[Decimal]
    priority: int
    start_price: Optional[Decimal]
    end_price: Optional[Decimal]
    sku_code: Optional[str] = None
    input_type: Optional[str] = None
    is_deleted: bool
    file_id: Optional[int] = None

    class Config:
        arbitrary_types_allowed = True

    @staticmethod
    def create_rack_rate_price_rule(hotel_id, rack_rate=None, file_id=None):
        return PricingRuleDto(
            hotel_id=hotel_id,
            rule_name=RuleType.DEFAULT_RACK_RATE.value,
            rule_type=RuleType.DEFAULT_RACK_RATE.value,
            config=None,
            is_input_price=True,
            priority=2,
            start_price=rack_rate,
            end_price=rack_rate,
            is_deleted=False,
            file_id=file_id,
        )

    @staticmethod
    def create_extra_adult_price_rule(hotel_id, input_price, extra_adult_price, subsequent_adult_price, file_id=None):
        return PricingRuleDto(
            hotel_id=hotel_id,
            rule_name=RuleType.EXTRA_ADULT_OCCUPANCY.value,
            rule_type=RuleType.EXTRA_ADULT_OCCUPANCY.value,
            config=ExtraAdultOccupancyRuleConfigDetail(extra_adult_price, subsequent_adult_price),
            is_input_price=True,
            priority=2,
            start_price=input_price,
            end_price=input_price,
            is_deleted=False,
            file_id=file_id,
        )

    @staticmethod
    def create_from_data(data):
        start_range = data.get("start_range")
        end_range = data.get("end_range")

        if data["rule_type"] == RuleType.TARGET_DATE.value:
            start_range = datetime.strptime(start_range, '%Y%m%d').date()
            end_range = datetime.strptime(end_range, '%Y%m%d').date()

        config_mapper = {
            RuleType.PERCENTAGE_OCCUPANCY.value:
                (OccupancyRangeRuleConfigDetail(occupancy_start=int(Decimal(start_range)),
                                                occupancy_end=int(Decimal(end_range)))
                 if data["rule_type"] == RuleType.PERCENTAGE_OCCUPANCY.value else None),
            RuleType.ADVANCE_BOOKING_WINDOW.value:
                (ABWRuleConfigDetail(abw_start=int(start_range), abw_end=int(end_range))
                 if data["rule_type"] == RuleType.ADVANCE_BOOKING_WINDOW.value else None),
            RuleType.TARGET_DATE.value:
                (TargetDateRuleConfigDetail(target_date=start_range)
                 if data["rule_type"] == RuleType.TARGET_DATE.value else None),
            RuleType.PERCENTAGE_OCCUPANCY_EVENING.value:
                (SameDayOccupancyRuleConfigDetail(occupancy_start=int(Decimal(start_range)),
                                                  occupancy_end=int(Decimal(end_range)))
                 if data["rule_type"] == RuleType.PERCENTAGE_OCCUPANCY_EVENING.value else None)
        }

        rule_name_type_mapping = {RuleType.PERCENTAGE_OCCUPANCY.value: "Occupancy",
                                  RuleType.ADVANCE_BOOKING_WINDOW.value: "ABW",
                                  RuleType.TARGET_DATE.value: "targetDate",
                                  RuleType.PERCENTAGE_OCCUPANCY_EVENING.value: "OccupancyEvening",
                                  RuleType.PRICE_BOUNDARIES.value: "priceBoundaries"
                                  }

        config = config_mapper.get(data["rule_type"])
        priority = 1
        if data.get('sku'):
            sku_prefix = data['sku']
            if 'extra-adult' not in sku_prefix.lower():
                priority = 2

        return PricingRuleDto(
            hotel_id=data["hotel_id"],
            rule_name=rule_name_type_mapping[data["rule_type"]],
            rule_type=data["rule_type"],
            config=config,
            is_input_price=True if data["rule_type"] == RuleType.PERCENTAGE_OCCUPANCY.value
            else False,
            factor=data.get("multiplier"),
            sum_factor=data.get("addition"),
            priority=priority,
            start_price=data.get("start_price"),
            end_price=data.get("end_price"),
            sku_code=data.get("sku"),
            input_type=data.get("input_type"),
            is_deleted=False,
            file_id=data.get("file_id"),
        )
