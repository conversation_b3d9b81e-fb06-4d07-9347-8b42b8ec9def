from datetime import datetime
from typing import Optional

from pydantic import BaseModel
from treebo_commons.utils import dateutils

from hawkeye.constants.hawkeye_constant import RuleFileUploadStatus


class RuleFileUploadDto(BaseModel):
    file_name: str
    path: Optional[str] = None
    uploaded_at: datetime
    status: RuleFileUploadStatus
    modified_at: datetime
    uploaded_by: Optional[str] = None

    @staticmethod
    def create_from_data(data):
        return RuleFileUploadDto(
            file_name=data.get("file_name"),
            path=data.get("path"),
            status=RuleFileUploadStatus.PENDING,
            uploaded_at=dateutils.current_datetime(),
            modified_at=dateutils.current_datetime(),
            uploaded_by=data.get("uploaded_by"),
        )
