import sentry_sdk

from hawkeye.application.csv_upload_handlers.competitive_hotel_mapping_csv_handler import (
    CompetitiveHotelMappingCSVHandler,
)
from hawkeye.application.csv_upload_handlers.enable_hotel_rate_ping_csv_handler import EnableHotelRatePingCSVHandler
from hawkeye.application.csv_upload_handlers.rule_file_csv_handler import RuleFileCSVHandler
from hawkeye.application.csv_upload_handlers.competitive_price_weightage_csv_handler import (
    CompetitivePriceWeightageCSVHandler,
)
from hawkeye.application.csv_upload_handlers.competiton_hotel_room_type_mapping_csv_handler import (
    CompetitiveHotelRoomTypeMappingCSVHandler,
)
from hawkeye.application.csv_upload_handlers.compset_pricing_thresholds_csv_handler import (
    CompsetPricingThresholdCSVHandler,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        CompetitivePriceWeightageCSVHandler,
        CompsetPricingThresholdCSV<PERSON>and<PERSON>,
        CompetitiveHotelRoomTypeMappingCSVHandler,
        CompetitiveHotelMappingCSVHandler,
        RuleFileCSVHandler,
        EnableHotelRatePingCSVHandler,
    ]
)
class FileUploadService:
    def __init__(
        self,
        competitive_price_weightage_csv_handler,
        compset_pricing_threshold_csv_handler,
        competitive_hotel_room_type_mapping_csv_handler,
        competitive_hotel_mapping_csv_handler,
        rule_file_csv_handler,
        enable_hotel_rate_ping_csv_handler,
    ):
        self.file_handlers = {
            "competitive_price_weightage": competitive_price_weightage_csv_handler,
            "compset_pricing_threshold": compset_pricing_threshold_csv_handler,
            "rule_file": rule_file_csv_handler,
            "competition_hotel_room_mappings": competitive_hotel_room_type_mapping_csv_handler,
            "competitive_hotel_mapping": competitive_hotel_mapping_csv_handler,
            "enable_hotel_rate_ping": enable_hotel_rate_ping_csv_handler,
        }

    def process_uploaded_file(self, action, uploaded_file, user_email):
        action_handler = self.file_handlers.get(action)
        return action_handler.process_file(uploaded_file, user_email)
