import base64
import datetime
import hashlib
import secrets

from sqladmin import Admin
from sqladmin.authentication import AuthenticationBackend

from starlette.requests import Request

from object_registry import locate_instance
from hawkeye.application.services.user_service import UserService
from hawkeye.infrastructure.cache.cache import get_cache


ALGORITHM = "pbkdf2_sha256"


def hash_password(password, salt=None, iterations=260000):
    if salt is None:
        salt = secrets.token_hex(16)
    assert salt and isinstance(salt, str) and "$" not in salt
    assert isinstance(password, str)
    pw_hash = hashlib.pbkdf2_hmac(
        "sha256", password.encode("utf-8"), salt.encode("utf-8"), iterations
    )
    b64_hash = base64.b64encode(pw_hash).decode("ascii").strip()
    return "{}${}${}${}".format(ALGORITHM, iterations, salt, b64_hash)


def verify_password(password, password_hash):
    if (password_hash or "").count("$") != 3:
        return False
    algorithm, iterations, salt, b64_hash = password_hash.split("$", 3)
    iterations = int(iterations)
    assert algorithm == ALGORITHM
    compare_hash = hash_password(password, salt, iterations)
    return secrets.compare_digest(password_hash, compare_hash)


class InMemorySession:
    def __init__(self):
        self.sessions = {}

    def create(self, username):
        session_id = secrets.token_hex(20)
        self.sessions[session_id] = {
            'username': username,
            'created_at': datetime.datetime.utcnow()
        }
        return session_id

    def get(self, session_id):
        return self.sessions[session_id]

    def delete(self, session_id):
        if session_id in self.sessions:
            del self.sessions[session_id]

    def validate(self, session_id):
        if session_id not in self.sessions:
            return

        session = self.sessions[session_id]
        now = datetime.datetime.utcnow()
        timediff = now - session['created_at']
        # Session should be valid for only 15 minutes.
        if timediff.days == 0 and timediff.seconds < 15*60:
            session['created_at'] = now
            return session['username']


class InCacheSession:

    def __init__(self, tenant_id):
        self.cache = get_cache(tenant_id)

    def create(self, username):
        session_id = secrets.token_hex(20)
        self.cache.set(session_id, username, 15*60)
        return session_id

    def get(self, session_id):
        return self.cache.get(session_id)

    def delete(self, session_id):
        self.cache.delete(session_id)

    def validate(self, session_id):
        username = self.cache.get(session_id)
        if username:
            self.cache.set(session_id, username, 15*60)

        return username


class AdminAuthBackend(AuthenticationBackend):

    def __init__(self, tenant_id, *args, **kwargs):
        self.store = InCacheSession(tenant_id)
        super(AdminAuthBackend, self).__init__(*args, **kwargs)

    async def login(self, request: Request) -> bool:
        form = await request.form()
        username, password = form["username"], form["password"]

        # Validate username/password credentials
        # And update session
        user_service = locate_instance(UserService)
        user = user_service.get_user(email=username)

        if not (user and verify_password(password, user.password)):
            return False

        session_id = self.store.create(username)
        request.session.update({"token": session_id})

        return True

    async def logout(self, request: Request) -> bool:
        token = request.session.get("token")
        self.store.delete(token)

        request.session.clear()
        return True

    async def authenticate(self, request: Request) -> bool:
        token = request.session.get("token")

        if not token:
            return False

        if self.store.validate(token):
            return True

        return False
