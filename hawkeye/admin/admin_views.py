import datetime
import os
import re

import sentry_sdk
from fastapi import UploadFile
from sqladmin import BaseView, ModelView, action, expose
from sqladmin.authentication import login_required
from sqlalchemy import or_, desc
from sqlalchemy.sql import select
from starlette.requests import Request
from starlette.responses import J<PERSON>NResponse, RedirectResponse
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.utils import dateutils
from wtforms.validators import ValidationError

from hawkeye.admin.authentication import AdminAuthBackend
from hawkeye.admin.request.schemas import PricePushRequest
from hawkeye.application.services.file_upload_service import FileUploadService
from hawkeye.application.services.price_alert_service import PriceAlertService
from hawkeye.application.services.price_calculation_service import (
    PriceCalculationService,
)
from hawkeye.constants.hawkeye_constant import (
    AUTHENTICATION_SECRET_KEY,
    PriceTriggerSource,
)
from hawkeye.domain.factory.price_calculation_trigger_factory import (
    PriceCalculationTriggerFactory,
)
from hawkeye.infrastructure.database.models import (
    BaseRoomTypePriceRuleModel,
    CompetitiveHotelMapping,
    CompetitiveHotelRoomTypeMapping,
    CompetitivePriceWeightage,
    CompsetPricingThreshold,
    HotelConfigModel,
    HotelRoomTypeModel,
    IncrementalPriceRuleModel,
    PriceAlert,
    PriceUpdateConfigModel,
    RoomTypePriceModel,
    RuleFileUploadModel,
)
from hawkeye.infrastructure.repositories.compset_pricing_threshold_repository import (
    CompsetPricingThresholdRepository,
)
from object_registry import locate_instance


EMAIL_REGEX = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,7}\b'


def format_datetime(m, a):
    return dateutils.localize_datetime(getattr(m, a)).strftime("%d-%m-%Y %H:%M")


class PriceUpdateConfigAdmin(ModelView, model=PriceUpdateConfigModel):
    column_list = [PriceUpdateConfigModel.id, PriceUpdateConfigModel.hour,
        PriceUpdateConfigModel.minute, PriceUpdateConfigModel.abw_start,
        PriceUpdateConfigModel.abw_end, PriceUpdateConfigModel.is_active,
        PriceUpdateConfigModel.created_at, PriceUpdateConfigModel.modified_at]
    form_excluded_columns = [HotelConfigModel.created_at, HotelConfigModel.modified_at]
    column_sortable_list = [PriceUpdateConfigModel.id, PriceUpdateConfigModel.hour,
        PriceUpdateConfigModel.minute, PriceUpdateConfigModel.is_active]
    column_default_sort = [
        (PriceUpdateConfigModel.hour, False),
        (PriceUpdateConfigModel.minute, False),
        (PriceUpdateConfigModel.abw_start, False),
    ]
    column_formatters = {
        PriceUpdateConfigModel.created_at: format_datetime,
        PriceUpdateConfigModel.modified_at: format_datetime,
    }
    can_create = False
    can_edit = False
    can_delete = False
    page_size = 25
    name = "Price push frequency"
    name_plural = "Price push frequencies"


class BaseRoomTypePriceRuleAdmin(ModelView, model=BaseRoomTypePriceRuleModel):
    column_list = [BaseRoomTypePriceRuleModel.rule_id, BaseRoomTypePriceRuleModel.hotel_id,
        BaseRoomTypePriceRuleModel.rule_name, BaseRoomTypePriceRuleModel.rule_type, BaseRoomTypePriceRuleModel.config,
        BaseRoomTypePriceRuleModel.is_input_price, BaseRoomTypePriceRuleModel.factor,
        BaseRoomTypePriceRuleModel.sum_factor, BaseRoomTypePriceRuleModel.priority,
        BaseRoomTypePriceRuleModel.start_price, BaseRoomTypePriceRuleModel.end_price,
        BaseRoomTypePriceRuleModel.is_deleted, BaseRoomTypePriceRuleModel.created_at, BaseRoomTypePriceRuleModel.modified_at]
    column_searchable_list = [BaseRoomTypePriceRuleModel.hotel_id, BaseRoomTypePriceRuleModel.rule_name]
    column_sortable_list = [BaseRoomTypePriceRuleModel.is_input_price, BaseRoomTypePriceRuleModel.priority,
        BaseRoomTypePriceRuleModel.is_deleted]
    column_default_sort = [
        (BaseRoomTypePriceRuleModel.is_deleted, False),
        (BaseRoomTypePriceRuleModel.hotel_id, False),
        (BaseRoomTypePriceRuleModel.created_at, True),
    ]
    column_formatters = {
        BaseRoomTypePriceRuleModel.created_at: format_datetime,
        BaseRoomTypePriceRuleModel.modified_at: format_datetime,
    }
    can_create = False
    can_edit = False
    can_delete = False
    name = "Rules Master List"
    name_plural = "Rules Master List"


class RuleFileUploadAdmin(ModelView, model=RuleFileUploadModel):
    column_list = [RuleFileUploadModel.id, RuleFileUploadModel.file_name,
        RuleFileUploadModel.path, RuleFileUploadModel.uploaded_at,
        RuleFileUploadModel.status, RuleFileUploadModel.modified_at]
    column_searchable_list = [RuleFileUploadModel.status]
    column_sortable_list = [RuleFileUploadModel.uploaded_at]
    column_default_sort = [
        (RuleFileUploadModel.uploaded_at, False),
    ]
    column_formatters = {
        RuleFileUploadModel.uploaded_at: format_datetime,
        RuleFileUploadModel.modified_at: format_datetime,
    }
    can_create = False
    can_edit = False
    can_delete = False
    name = "Upload pricing factors"
    name_plural = "Upload pricing factors"


def validate_hotel_is_enabled(form, field):
    if not field.data:
        return

    price_calculation_service = locate_instance(PriceCalculationService)
    errors = price_calculation_service.validate_hotel_can_be_enabled(hotel_id=form.hotel_id.data)
    if errors:
        raise ValidationError(errors)


def validate_hotel_is_competitor_price_enabled(form, field):
    if not field.data:
        return

    if not form.revenue_poc_emails.data:
        raise ValidationError("Revenue POC Emails are required to enable competitive price.")


def validate_hotel_revenue_poc_emails(form, field):
    if not field.data:
        return

    for email in field.data.split(','):
        if not re.fullmatch(EMAIL_REGEX, email):
            raise ValidationError("Revenue POC Emails are required to enable competitive price.")


class HotelConfigAdmin(ModelView, model=HotelConfigModel):
    column_list = [HotelConfigModel.hotel_id, HotelConfigModel.is_enabled,
        HotelConfigModel.is_live, HotelConfigModel.is_competitor_price_enabled,
        HotelConfigModel.revenue_poc_emails, HotelConfigModel.created_at,
        HotelConfigModel.modified_at]
    form_columns = [HotelConfigModel.hotel_id, HotelConfigModel.is_enabled,
        HotelConfigModel.is_competitor_price_enabled, HotelConfigModel.revenue_poc_emails]
    column_searchable_list = [HotelConfigModel.hotel_id]
    column_sortable_list = [HotelConfigModel.is_enabled, HotelConfigModel.is_live,
        HotelConfigModel.is_competitor_price_enabled]
    column_default_sort = [
        (HotelConfigModel.is_enabled, True),
        (HotelConfigModel.modified_at, False),
    ]
    column_formatters = {
        HotelConfigModel.created_at: format_datetime,
        HotelConfigModel.modified_at: format_datetime,
    }
    form_include_pk = True
    form_widget_args = {
        "hotel_id": {
            "readonly": True,
        },
    }
    form_args = {
        "is_enabled": {
            "validators": [validate_hotel_is_enabled],
        },
        "is_competitor_price_enabled": {
            "validators": [validate_hotel_is_competitor_price_enabled],
        },
        "revenue_poc_emails": {
            "validators": [validate_hotel_revenue_poc_emails],
        },
    }
    can_create = False
    can_delete = False
    name = "Hotel Master List"
    name_plural = "Hotel Master List"
    icon = "fa fa-cog"


class RoomTypePriceAdmin(ModelView, model=RoomTypePriceModel):
    column_list = [RoomTypePriceModel.id, RoomTypePriceModel.hotel_id,
                   RoomTypePriceModel.room_type, RoomTypePriceModel.created_at,
                   RoomTypePriceModel.target_date, RoomTypePriceModel.input_price,
                   RoomTypePriceModel.final_price, RoomTypePriceModel.occupancy_percentage,
                   RoomTypePriceModel.is_published]
    column_searchable_list = [RoomTypePriceModel.hotel_id, RoomTypePriceModel.room_type]
    column_sortable_list = [RoomTypePriceModel.target_date]
    column_default_sort = [
        (RoomTypePriceModel.is_published, True),
        (RoomTypePriceModel.hotel_id, False),
        (RoomTypePriceModel.created_at, True),
        (RoomTypePriceModel.target_date, False),
    ]
    column_formatters = {
        RoomTypePriceModel.created_at: format_datetime,
    }
    can_create = False
    can_edit = False
    can_delete = False
    name = "Pricing Audit Trail"
    name_plural = "Pricing Audit Trail"


class HotelRoomTypeAdmin(ModelView, model=HotelRoomTypeModel):
    column_list = [HotelRoomTypeModel.id, HotelRoomTypeModel.hotel_id,
        HotelRoomTypeModel.room_type_code, HotelRoomTypeModel.room_type_name,
        HotelRoomTypeModel.is_base_room_type, HotelRoomTypeModel.total_rooms,
        HotelRoomTypeModel.is_active]
    column_searchable_list = [HotelRoomTypeModel.hotel_id, HotelRoomTypeModel.room_type_name]
    column_default_sort = [
        (HotelRoomTypeModel.hotel_id, False),
    ]
    can_create = False
    can_edit = True
    can_delete = False
    name = "Base Room Master List"
    name_plural = "Base Room Master List"
    icon = "fa fa-cog"
    form_widget_args = {
        "hotel_id": {
            "readonly": True,
        },
        "room_type_code": {
            "readonly": True,
        },
        "room_type_name": {
            "readonly": True,
        },
        "total_rooms": {
            "readonly": True,
        },
        "is_active": {
            "readonly": True,
        },
        "created_at": {
            "readonly": True,
        },
        "modified_at": {
            "readonly": True,
        },
    }

    async def update_model(self, request, pk,  data):
        data['modified_at'] = datetime.datetime.utcnow()
        return await super().update_model(request, pk, data)


class IncrementalPriceRuleAdmin(ModelView, model=IncrementalPriceRuleModel):
    column_list = [IncrementalPriceRuleModel.rule_id, IncrementalPriceRuleModel.hotel_id,
                   IncrementalPriceRuleModel.room_type, IncrementalPriceRuleModel.factor,
                   IncrementalPriceRuleModel.sum_factor, IncrementalPriceRuleModel.is_deleted,
                   IncrementalPriceRuleModel.created_at, IncrementalPriceRuleModel.modified_at]
    form_excluded_columns = [IncrementalPriceRuleModel.is_deleted, HotelConfigModel.created_at, HotelConfigModel.modified_at]
    column_searchable_list = [IncrementalPriceRuleModel.hotel_id, IncrementalPriceRuleModel.room_type]
    column_sortable_list = [IncrementalPriceRuleModel.is_deleted]
    column_default_sort = [
        (IncrementalPriceRuleModel.is_deleted, False),
        (IncrementalPriceRuleModel.hotel_id, False),
    ]
    can_delete = False
    name = "Room Upgrade Price"
    name_plural = "Room Upgrade Price"
    icon = "fa fa-cog"
    form_widget_args = {
        "factor": {
            "readonly": True,
        },
    }
    create_template = "create_room_price_upgrade.html"


def validate_future_stay_date(form, field):
    if not field.data:
        return

    if field.data < datetime.date.today():
        raise ValidationError("Stay date should be in future.")


def validate_percentage_value(form, field):
    if not field.data:
        return

    if field.data < 0:
        raise ValidationError("Percentage value should be positive value.")


class CompsetPricingThresholdAdmin(ModelView, model=CompsetPricingThreshold):
    column_list = [
        CompsetPricingThreshold.hotel_id,
        CompsetPricingThreshold.stay_date,
        CompsetPricingThreshold.increased_percentage_threshold,
        CompsetPricingThreshold.decreased_percentage_threshold,
        CompsetPricingThreshold.normalized_competitive_price_avg,
        CompsetPricingThreshold.is_deleted,
    ]
    form_columns = [
        CompsetPricingThreshold.hotel_id,
        CompsetPricingThreshold.stay_date,
        CompsetPricingThreshold.increased_percentage_threshold,
        CompsetPricingThreshold.decreased_percentage_threshold,
    ]
    column_searchable_list = [CompsetPricingThreshold.hotel_id, CompsetPricingThreshold.stay_date]
    column_sortable_list = [CompsetPricingThreshold.stay_date]
    column_default_sort = [
        (CompsetPricingThreshold.hotel_id, False),
        (CompsetPricingThreshold.stay_date, False),
    ]
    column_formatters = {
        CompsetPricingThreshold.created_at: format_datetime,
        CompsetPricingThreshold.modified_at: format_datetime,
    }
    form_args = {
        "stay_date": {
            "validators": [validate_future_stay_date],
        },
        "increased_percentage_threshold": {
            "validators": [validate_percentage_value],
        },
        "decreased_percentage_threshold": {
            "validators": [validate_percentage_value],
        },
    }
    can_create = True
    can_edit = True
    name = "Compset (Datewise)"
    name_plural = "Compset (Datewise)"
    category = "COMPETITION PRICING"
    icon = "fa fa-balance-scale"

    async def on_model_change(self, data, model, is_created, request: Request):
        if is_created:
            compset_pricing_threshold_repository = locate_instance(CompsetPricingThresholdRepository)
            existing_threshold = compset_pricing_threshold_repository.load_all_for_hotel(data["hotel_id"], [data["stay_date"]])

            if existing_threshold:
                raise ValidationError(
                    "Compset already exists for this hotel and stay date. Mark it deleted to create new one."
                )
        await super().on_model_change(data, model, is_created, request)


class ManualPricePushAdmin(BaseView):
    name = "Manual price push"
    category = "ACTIONS & UPLOADS"
    icon = "fa-solid fa-chart-line"

    def __init__(self, *args, **kwargs):
        tenant_id = os.environ.get('ADMIN_TENANT_ID', TenantClient.get_default_tenant())
        self.authentication_backend = AdminAuthBackend(tenant_id, secret_key=AUTHENTICATION_SECRET_KEY)
        super(ManualPricePushAdmin, self).__init__(*args, **kwargs)

    @login_required
    @expose("/price_push", methods=["GET"])
    async def load_price_push_page(self, request: Request):
        return await self.templates.TemplateResponse(
            request,
            "manual_price_push.html",
        )

    @login_required
    @expose("/price_push", methods=["POST"])
    async def price_push(self, request: Request):
        """
        This api triggers price calculation which publish price calculation event
        which later on triggers price push to rackrate & ratemanager
        """
        try:
            user_email = self.authentication_backend.store.validate(request.session['token'])
            request_body = await request.json()
            price_push_request = PricePushRequest(**request_body)
            price_push_request.strip_commas()
            price_calculation_service = locate_instance(PriceCalculationService)
            price_calculation_trigger = PriceCalculationTriggerFactory. \
                create_price_calculation_trigger_from_price_push_request(PriceTriggerSource.MANUAL, price_push_request,
                                                                         user_email)
            price_calculation_service.trigger_price_calculation(config_date_time=None, price_calculation_trigger=price_calculation_trigger)
        except Exception as ex:
            sentry_sdk.capture_exception(ex)
            return JSONResponse({"response": f"Price push failed", "error": str(ex)})

        return JSONResponse({"response": "Successfully triggered price push", "status": 200})


class RepublishLastPriceAdmin(BaseView):
    name = "Republish last price"
    category = "ACTIONS & UPLOADS"
    icon = "fa-solid fa-chart-line"

    def __init__(self, *args, **kwargs):
        tenant_id = os.environ.get('ADMIN_TENANT_ID', TenantClient.get_default_tenant())
        self.authentication_backend = AdminAuthBackend(tenant_id, secret_key=AUTHENTICATION_SECRET_KEY)
        super(RepublishLastPriceAdmin, self).__init__(*args, **kwargs)

    @login_required
    @expose("/re_publish_price", methods=["GET"])
    async def load_re_publish_price_page(self, request: Request):
        return await self.templates.TemplateResponse(
            request,
            "re_publish_price.html",
        )

    @login_required
    @expose("/re_publish_price", methods=["POST"])
    async def re_publish_price(self, request: Request):
        """
        This api triggers price calculation which publish price calculation event
        which later on triggers price push to rackrate & ratemanager
        """
        try:
            user_email = self.authentication_backend.store.validate(request.session['token'])
            request_body = await request.json()
            price_calculation_service = locate_instance(PriceCalculationService)
            price_calculation_service.re_publish_price(**request_body)
        except Exception as ex:
            sentry_sdk.capture_exception(ex)
            return JSONResponse({"response": f"Republish Price failed ", "error": str(ex)})

        return JSONResponse({"response": "Price republished successfully", "status": 200})


class FileUploadAdmin(BaseView):
    name = "File Uploads"
    category = "ACTIONS & UPLOADS"
    icon = "fa-solid fa-chart-line"

    def __init__(self, *args, **kwargs):
        tenant_id = os.environ.get('ADMIN_TENANT_ID', TenantClient.get_default_tenant())
        self.authentication_backend = AdminAuthBackend(tenant_id, secret_key=AUTHENTICATION_SECRET_KEY)
        super(FileUploadAdmin, self).__init__(*args, **kwargs)

    @login_required
    @expose("/upload_file", methods=["GET"])
    async def load_file_upload_admin(self, request: Request):
        return await self.templates.TemplateResponse(
            request,
            "file_uploads.html",
            context={"request": request},
        )

    @login_required
    @expose("/upload_file", methods=["POST"])
    async def process_uploaded_file(
        self,
        request: Request,
    ):
        try:
            user_email = self.authentication_backend.store.validate(request.session['token'])
            request_form = await request.form()
            file_type = request_form.get("file_type")
            uploaded_file: UploadFile = request_form.get("file")
            file_upload_service = locate_instance(FileUploadService)
            failed_rows = file_upload_service.process_uploaded_file(
                file_type, uploaded_file, user_email
            )

            response_data = {"response": "File Uploaded successfully", "status": 200}
            if failed_rows:
                response_data["failed_rows"] = failed_rows
            return JSONResponse(response_data)

        except Exception as ex:
            sentry_sdk.capture_exception(ex)
            return JSONResponse(
                {"response": "Error Occurred while uploading file", "error": str(ex)}, status_code=400
            )


class CompetitiveHotelMappingAdmin(ModelView, model=CompetitiveHotelMapping):
    column_list = [
        CompetitiveHotelMapping.internal_hotel_id,
        CompetitiveHotelMapping.competitive_hotel_id,
        CompetitiveHotelMapping.is_active,
        CompetitiveHotelMapping.normalization_factor,
        CompetitiveHotelMapping.created_at,
        CompetitiveHotelMapping.modified_at,
    ]
    form_columns = [
        CompetitiveHotelMapping.internal_hotel_id,
        CompetitiveHotelMapping.competitive_hotel_id,
        CompetitiveHotelMapping.is_active,
        CompetitiveHotelMapping.normalization_factor,
    ]
    column_searchable_list = [CompetitiveHotelMapping.internal_hotel_id, CompetitiveHotelMapping.competitive_hotel_id]
    column_sortable_list = [CompetitiveHotelMapping.internal_hotel_id, CompetitiveHotelMapping.competitive_hotel_id]
    column_default_sort = [(CompetitiveHotelMapping.internal_hotel_id, False)]
    column_formatters = {
        CompetitiveHotelMapping.created_at: format_datetime,
        CompetitiveHotelMapping.modified_at: format_datetime,
    }
    can_create = True
    can_edit = True
    can_delete = False
    name = "Competitive Hotel Mapping"
    name_plural = "Competitive Hotel Mappings"
    category = "COMPETITION PRICING"
    icon = "fa-solid fa-hotel"


class PriceAlertAdmin(ModelView, model=PriceAlert):
    column_list = [
        PriceAlert.hotel_id,
        PriceAlert.stay_date,
        PriceAlert.competitive_price,
        PriceAlert.hawkeye_price,
        PriceAlert.threshold_price,
        PriceAlert.suggested_change_percentage,
        PriceAlert.alert_threshold_percentage,
        PriceAlert.price_trigger_id,
        PriceAlert.is_expired,
    ]

    column_searchable_list = [PriceAlert.hotel_id, PriceAlert.stay_date]
    column_sortable_list = [PriceAlert.hotel_id, PriceAlert.stay_date]
    column_default_sort = [(PriceAlert.hotel_id, False)]

    column_formatters = {
        PriceAlert.created_at: format_datetime,
        PriceAlert.modified_at: format_datetime,
    }

    can_create = False
    can_edit = False
    can_delete = False
    name = "Price Alert"
    name_plural = "Price Alerts"
    category = "COMPETITION PRICING"
    icon = "fa-solid fa-envelope-circle-check"

    def __init__(self, *args, **kwargs):
        tenant_id = os.environ.get('ADMIN_TENANT_ID', TenantClient.get_default_tenant())
        self.authentication_backend = AdminAuthBackend(tenant_id, secret_key=AUTHENTICATION_SECRET_KEY)
        super(PriceAlertAdmin, self).__init__(*args, **kwargs)

    def list_query(self, request):
        user_email = self.authentication_backend.store.validate(request.session['token'])
        hotel_subquery = (
            select(HotelConfigModel.hotel_id)
            .where(HotelConfigModel.revenue_poc_emails.contains(user_email))
        )

        return select(PriceAlert).where(
            PriceAlert.hotel_id.in_(hotel_subquery),
            PriceAlert.is_expired == False,
            or_(PriceAlert.approver_email == '', PriceAlert.approver_email.is_(None))
        ).order_by(desc(PriceAlert.created_at))

    @action(
        name="approve_alert",
        label="Approve",
        confirmation_message="Do you want to approve this alert?",
        add_in_list=False,
        add_in_detail=True,
    )
    async def approve_alert(self, request: Request):
        user_email = self.authentication_backend.store.validate(request.session['token'])
        price_alert_id = request.query_params.get('pks')
        price_alert_service = locate_instance(PriceAlertService)
        price_alert_service.approve_price_alert(price_alert_id, user_email)
        referer = request.headers.get("Referer")
        if referer:
            return RedirectResponse(referer)
        else:
            return RedirectResponse(request.url_for("admin:list", identity=self.identity))


class CompetitivePriceWeightageAdmin(ModelView, model=CompetitivePriceWeightage):
    name = "Competitive Price Weightage"
    name_plural = "Competitive Price Weightages"
    icon = "fa fa-balance-scale"
    category = "COMPETITION PRICING"
    can_delete = False

    column_list = [
        CompetitivePriceWeightage.id,
        CompetitivePriceWeightage.hotel_id,
        CompetitivePriceWeightage.occupancy_start,
        CompetitivePriceWeightage.occupancy_end,
        CompetitivePriceWeightage.lower_competitive_price_multiplier,
        CompetitivePriceWeightage.higher_competitive_price_multiplier,
        CompetitivePriceWeightage.file_id,
        CompetitivePriceWeightage.is_deleted
    ]

    column_searchable_list = [CompetitivePriceWeightage.hotel_id]

    column_sortable_list = [
        CompetitivePriceWeightage.id,
        CompetitivePriceWeightage.hotel_id,
        CompetitivePriceWeightage.occupancy_start
    ]

    form_columns = [
        CompetitivePriceWeightage.hotel_id,
        CompetitivePriceWeightage.occupancy_start,
        CompetitivePriceWeightage.occupancy_end,
        CompetitivePriceWeightage.lower_competitive_price_multiplier,
        CompetitivePriceWeightage.higher_competitive_price_multiplier,
    ]


class CompetitiveHotelRoomTypeMappingAdmin(ModelView, model=CompetitiveHotelRoomTypeMapping):
    name = "Competitive Hotel Room Type Mapping"
    name_plural = "Competitive Hotel Room Type Mappings"
    category = "COMPETITION PRICING"
    icon = "fa-solid fa-hotel"

    column_list = [
        CompetitiveHotelRoomTypeMapping.id,
        CompetitiveHotelRoomTypeMapping.competitive_hotel_id,
        CompetitiveHotelRoomTypeMapping.competitive_hotel_room_type,
        CompetitiveHotelRoomTypeMapping.internal_hotel_id,
        CompetitiveHotelRoomTypeMapping.internal_hotel_room_type,
        CompetitiveHotelRoomTypeMapping.file_id,
        CompetitiveHotelRoomTypeMapping.is_active,
    ]

    column_searchable_list = [
        CompetitiveHotelRoomTypeMapping.competitive_hotel_id,
        CompetitiveHotelRoomTypeMapping.internal_hotel_id,
    ]

    form_columns = [
        CompetitiveHotelRoomTypeMapping.competitive_hotel_id,
        CompetitiveHotelRoomTypeMapping.competitive_hotel_room_type,
        CompetitiveHotelRoomTypeMapping.internal_hotel_id,
        CompetitiveHotelRoomTypeMapping.internal_hotel_room_type,
        CompetitiveHotelRoomTypeMapping.is_active,
    ]

    can_create = True
    can_edit = True
    can_delete = False


def get_admin_views():
    views = [
        PriceUpdateConfigAdmin,
        BaseRoomTypePriceRuleAdmin,
        RuleFileUploadAdmin,
        HotelConfigAdmin,
        HotelRoomTypeAdmin,
        IncrementalPriceRuleAdmin,
        RoomTypePriceAdmin,
        RepublishLastPriceAdmin,
        ManualPricePushAdmin,
        FileUploadAdmin,
        CompsetPricingThresholdAdmin,
        CompetitivePriceWeightageAdmin,
        CompetitiveHotelMappingAdmin,
        CompetitiveHotelRoomTypeMappingAdmin,
        PriceAlertAdmin
    ]
    return views
