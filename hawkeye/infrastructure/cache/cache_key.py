class KeyConvertor(object):

    @staticmethod
    def make_final_price_key(hotel_id, target_date, sku_code):
        key_string = f"final_price_{hotel_id}_{sku_code}_{target_date}"
        return key_string

    @staticmethod
    def make_sku_code_key(sku_name, adults=None):
        sku_name = sku_name.replace('-', '_')
        if adults:
            key_string = f"sku_code_{sku_name}_{adults}"
        else:
            key_string = f"sku_code_{sku_name}"
        return key_string
