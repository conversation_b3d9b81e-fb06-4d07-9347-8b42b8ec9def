import logging
from typing import List

from hawkeye.domain.entities.hotel_config import HotelConfig
from hawkeye.infrastructure.database.models import HotelConfigModel
from hawkeye.infrastructure.exception import DatabaseError
from hawkeye.infrastructure.repositories.adaptors.hotel_config_adaptor import HotelConfigAdaptor
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class HotelConfigRepository(BaseRepository):
    _model = HotelConfigModel
    _adaptor = HotelConfigAdaptor()

    def create(self, hotel_config: HotelConfig):
        hotel_config_model = self._adaptor.to_db_entity(domain_entity=hotel_config)
        try:
            self._save(hotel_config_model)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def update(self, hotel_config: HotelConfig):
        hotel_config_model = self._adaptor.to_db_entity(hotel_config)
        try:
            self._update(hotel_config_model)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def update_all(self, hotel_configs: List[HotelConfig]):
        hotel_config_models = [
            self._adaptor.to_db_entity(hotel_config) for hotel_config in
            hotel_configs
        ]
        try:
            self._update_all(hotel_config_models)
        except Exception:
            raise DatabaseError

    def load_all_enabled(self, hotel_ids=None):
        if hotel_ids:
            enabled_hotel_models = self.query(self._model).filter(self._model.hotel_id.in_(hotel_ids),
                                                                  self._model.is_live == True,
                                                                  self._model.is_enabled == True).all()
        else:
            enabled_hotel_models = self.query(self._model).filter(self._model.is_live == True,
                                                                  self._model.is_enabled == True).all()
        return [self._adaptor.to_domain_entity(hotel_config_model) for hotel_config_model in enabled_hotel_models]

    def load_all(self):
        hotel_config_models = self.query(self._model).all()
        return [self._adaptor.to_domain_entity(hotel_config_model) for hotel_config_model in hotel_config_models]

    def load_for_update(self, hotel_id=None):
        return self._adaptor.to_domain_entity(
            self.get_for_update(HotelConfigModel, hotel_id=hotel_id))

    def load_all_for_update(self, hotel_ids):
        hotel_models = self.query(self._model).filter(self._model.hotel_id.in_(hotel_ids)).all()

        return [self._adaptor.to_domain_entity(hotel_config_model) for hotel_config_model in hotel_models]


    def create_all(self, hotel_configs: List[HotelConfig]):
        hotel_config_models = [self._adaptor.to_db_entity(domain_entity=hotel_config) for hotel_config in hotel_configs]
        try:
            self._save_all(hotel_config_models)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def load(self, hotel_id):
        hotel_config_model = self.query(self._model).filter(self._model.hotel_id == hotel_id).first()
        if not hotel_config_model:
            return
        return self._adaptor.to_domain_entity(hotel_config_model)
