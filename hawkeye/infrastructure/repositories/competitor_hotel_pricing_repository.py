import logging
from datetime import datetime

from sqlalchemy.orm import aliased
from sqlalchemy.sql import case
from sqlalchemy import select, func, and_, literal

from hawkeye.domain.entities.competitor_hotel_pricing import CompetitorHotelPricing
from hawkeye.infrastructure.database.models import (
    CompetitorHotelPricing as CompetitorHotelPricingModel,
    CompetitiveHotelRoomTypeMapping as CompetitiveHotelRoomTypeMappingModel,
    HotelRoomTypeModel,
)
from hawkeye.infrastructure.exception import DatabaseError
from hawkeye.infrastructure.repositories.adaptors.competitor_hotel_pricing_adaptor import (
    CompetitorHotelPricingAdaptor,
)
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class CompetitorHotelPricingRepository(BaseRepository):
    _model = CompetitorHotelPricingModel
    _room_type_map_model = CompetitiveHotelRoomTypeMappingModel
    _hotel_room_type_model = HotelRoomTypeModel
    _adaptor = CompetitorHotelPricingAdaptor()

    def load_competitor_prices(self, competitor_hotel_ids, stay_dates):
        competitor_hotel_pricing_models = self.query(self._model)
        competitor_hotel_pricing_models = competitor_hotel_pricing_models.filter(
            self._model.competitor_hotel_id.in_(competitor_hotel_ids),
            self._model.stay_date.in_(stay_dates),
            self._model.expires_at > datetime.now(),
        )
        competitor_hotel_pricing_models = competitor_hotel_pricing_models.all()
        return [
            self._adaptor.to_domain_entity(competitor_hotel_pricing_model)
            for competitor_hotel_pricing_model in competitor_hotel_pricing_models
        ]

    def load_competitor_prices_of_hotel_competitor(
        self,
        internal_hotel_id,
        competitor_hotel_id,
        stay_dates,
    ):
        chp = aliased(self._model)
        chrtm = aliased(self._room_type_map_model)
        hrt = aliased(self._hotel_room_type_model)

        # Subquery to get the base room type name
        base_room_type_subquery = (
            select(hrt.room_type_name)
            .where(
                and_(
                    hrt.hotel_id == internal_hotel_id,
                    hrt.is_base_room_type.is_(True),
                    hrt.is_active.is_(True),
                )
            )
            .limit(1)  # Ensure only one result is returned
            .scalar_subquery()
        )

        # Base query with LEFT JOIN to match room types
        mapped_prices = (
            select(
                chp.competitor_hotel_id,
                chp.stay_date,
                chp.room_type,
                chp.price,
                chp.id,
                chrtm.internal_hotel_id,
                chrtm.internal_hotel_room_type,
                case(
                    (chrtm.internal_hotel_room_type != None, literal(1)),  # Mapped room priority
                    else_=literal(2)  # Lowest price fallback
                ).label("preference_rank")
            )
            .select_from(chp)
            .outerjoin(
                chrtm,
                and_(
                    chp.competitor_hotel_id == chrtm.competitive_hotel_id,
                    chp.room_type == chrtm.competitive_hotel_room_type,
                    chrtm.internal_hotel_id == internal_hotel_id,
                    chrtm.internal_hotel_room_type == base_room_type_subquery,
                    chrtm.is_active.is_(True),
                ),
            )
            .where(
                and_(
                    chp.competitor_hotel_id == competitor_hotel_id,
                    chp.stay_date.in_(stay_dates),
                    chp.expires_at > func.now(),
                )
            )
            .subquery()
        )

        # Apply row_number() in a CTE
        ranked_prices = (
            select(
                mapped_prices.c.competitor_hotel_id,
                mapped_prices.c.stay_date,
                mapped_prices.c.room_type,
                mapped_prices.c.price,
                mapped_prices.c.id,
                func.row_number()
                .over(
                    partition_by=[mapped_prices.c.competitor_hotel_id, mapped_prices.c.stay_date],
                    order_by=[mapped_prices.c.preference_rank, mapped_prices.c.price]
                )
                .label("row_num")
            )
        ).cte("ranked_prices")  # <-- Common Table Expression (CTE)

        # Final query: Select only rows where row_num = 1
        final_query = (
            select(
                ranked_prices.c.id,
                ranked_prices.c.competitor_hotel_id,
                ranked_prices.c.stay_date,
                ranked_prices.c.room_type,
                ranked_prices.c.price
            )
            .where(ranked_prices.c.row_num == 1)  # Now allowed because it's in an outer query
        )

        results = self.session().execute(final_query).fetchall()

        competitor_hotel_pricing_models = self.query(self._model)
        competitor_hotel_pricing_models = competitor_hotel_pricing_models.filter(
            self._model.id.in_(row["id"] for row in results),
        )
        competitor_hotel_pricing_models = competitor_hotel_pricing_models.all()
        return [
            self._adaptor.to_domain_entity(competitor_hotel_pricing_model)
            for competitor_hotel_pricing_model in competitor_hotel_pricing_models
        ]

    def create(self, competitor_hotel_pricing: CompetitorHotelPricing):
        competitor_hotel_pricing_model = self._adaptor.to_db_entity(competitor_hotel_pricing)
        try:
            self._save(competitor_hotel_pricing_model)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def update(self, competitor_hotel_pricing: CompetitorHotelPricing):
        competitor_hotel_pricing_model = self._adaptor.to_db_entity(competitor_hotel_pricing)
        try:
            self._update(competitor_hotel_pricing_model)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def update_all(self, competitor_hotel_pricings: [CompetitorHotelPricing]):
        competitor_hotel_pricing_models = [
            self._adaptor.to_db_entity(competitor_hotel_price)
            for competitor_hotel_price in competitor_hotel_pricings
        ]
        try:
            self._update_all(competitor_hotel_pricing_models)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def create_all(self, competitor_hotel_pricings: [CompetitorHotelPricing]):
        competitor_hotel_pricing_models = [
            self._adaptor.to_db_entity(competitor_hotel_price)
            for competitor_hotel_price in competitor_hotel_pricings
        ]
        try:
            super().create_all(competitor_hotel_pricing_models)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())
