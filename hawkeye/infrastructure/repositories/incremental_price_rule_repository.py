import logging

from hawkeye.infrastructure.database.models import IncrementalPriceRuleModel
from hawkeye.infrastructure.repositories.adaptors.incremental_price_rule_adaptor import IncrementalPriceRuleAdaptor
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class IncrementalPriceRuleRepository(BaseRepository):
    _model = IncrementalPriceRuleModel
    _adaptor = IncrementalPriceRuleAdaptor()

    def load_all(self, hotel_ids):
        if hotel_ids:
            price_rule_models = self.query(self._model).filter(self._model.hotel_id.in_(hotel_ids),
                                                               self._model.is_deleted == False).all()
        else:
            price_rule_models = self.query(self._model).filter(self._model.is_deleted == False).all()
        return [self._adaptor.to_domain_entity(price_rule_model) for price_rule_model in price_rule_models]
