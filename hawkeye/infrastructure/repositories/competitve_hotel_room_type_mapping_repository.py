from hawkeye.infrastructure.database.models import CompetitiveHotelRoomTypeMapping
from hawkeye.infrastructure.exception import DatabaseError
from hawkeye.infrastructure.repositories.adaptors.competitive_hotel_room_type_mapping_adaptor import (
    CompetitiveHotelRoomTypeMappingAdaptor,
)
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class CompetitiveHotelRoomTypeMappingRepository(BaseRepository):
    _model = CompetitiveHotelRoomTypeMapping
    _adaptor = CompetitiveHotelRoomTypeMappingAdaptor()

    def load_all_mappings(self, internal_hotel_id, competitive_hotel_id=None):
        query = self.session().query(self._model).filter(
            self._model.internal_hotel_id == internal_hotel_id
        )
        if competitive_hotel_id:
            query = query.filter(self._model.competitive_hotel_id == competitive_hotel_id)

        room_type_mapping_models = query.all()
        return [
            self._adaptor.to_domain_entity(room_type_mapping_model)
            for room_type_mapping_model in room_type_mapping_models
        ]

    def update_all(self, mappings):
        room_type_mapping_models = [self._adaptor.to_db_entity(mapping) for mapping in mappings]
        try:
            super()._update_all(room_type_mapping_models)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def create_all(self, mappings):
        room_type_mapping_models = [self._adaptor.to_db_entity(mapping) for mapping in mappings]
        try:
            super().create_all(room_type_mapping_models)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())
