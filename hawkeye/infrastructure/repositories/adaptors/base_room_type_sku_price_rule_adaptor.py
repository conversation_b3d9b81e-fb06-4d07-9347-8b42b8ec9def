from hawkeye.constants.hawkeye_constant import RuleType
from hawkeye.domain.entities.base_room_type_sku_price_rule import BaseRoomTypeSkuPriceRule
from hawkeye.domain.value_objects.rule_definition import RuleDefinition
from hawkeye.infrastructure.database.models import BaseRoomTypeSkuPriceRuleModel
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import BaseAdaptor


class BaseRoomTypeSkuPriceRuleAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: BaseRoomTypeSkuPriceRule):
        return BaseRoomTypeSkuPriceRuleModel(
            rule_id=domain_entity.rule_id,
            hotel_id=domain_entity.hotel_id,
            sku_code=domain_entity.sku_code,
            input_type=domain_entity.input_type,
            rule_name=domain_entity.rule_name,
            rule_type=domain_entity.rule_type,
            config=domain_entity.config.to_json() if domain_entity.config else None,
            factor=domain_entity.factor,
            sum_factor=domain_entity.sum_factor,
            priority=domain_entity.priority,
            start_price=domain_entity.start_price,
            end_price=domain_entity.end_price,
            is_deleted=domain_entity.is_deleted,
            file_id=domain_entity.file_id
        )

    def to_domain_entity(self, db_model: BaseRoomTypeSkuPriceRuleModel) -> BaseRoomTypeSkuPriceRule:
        rule_type = RuleType(db_model.rule_type)
        rule_config_detail_class = RuleDefinition.RULE_CONFIG_DETAIL_CLASS_MAP.get(
            rule_type
        )
        return BaseRoomTypeSkuPriceRule(
            rule_id=db_model.rule_id,
            hotel_id=db_model.hotel_id,
            sku_code=db_model.sku_code,
            input_type=db_model.input_type,
            rule_name=db_model.rule_name,
            rule_type=db_model.rule_type,
            config=rule_config_detail_class.from_json(
                rule_detail_json=db_model.config
            )
            if rule_config_detail_class
            else None,
            factor=db_model.factor,
            sum_factor=db_model.sum_factor,
            priority=db_model.priority,
            start_price=db_model.start_price,
            end_price=db_model.end_price,
            created_at=db_model.created_at,
            is_deleted=db_model.is_deleted,
            file_id=db_model.file_id,
        )
