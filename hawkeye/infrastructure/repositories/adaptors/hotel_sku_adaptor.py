from hawkeye.domain.entities.hotel_sku import HotelSku
from hawkeye.infrastructure.database.models import HotelSkuModel
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import BaseAdaptor


class HotelSkuAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: HotelSku):
        return HotelSkuModel(
            id=domain_entity.id,
            hotel_id=domain_entity.hotel_id,
            sku_code=domain_entity.sku_code,
            sku_category=domain_entity.sku_category,
            rack_rate=domain_entity.rack_rate,
            description=domain_entity.description,
            extra_information=domain_entity.extra_information,
            sku_name=domain_entity.sku_name,
            is_active=domain_entity.is_active,
        )

    def to_domain_entity(self, db_model: HotelSkuModel) -> HotelSku:
        return HotelSku(
            id=db_model.id,
            hotel_id=db_model.hotel_id,
            sku_code=db_model.sku_code,
            sku_category=db_model.sku_category,
            rack_rate=db_model.rack_rate,
            description=db_model.description,
            extra_information=db_model.extra_information,
            sku_name=db_model.sku_name,
            is_active=db_model.is_active,
        )
