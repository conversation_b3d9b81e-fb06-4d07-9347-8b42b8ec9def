from hawkeye.domain.entities.rate_info import RateInfoEntity
from hawkeye.infrastructure.database.models import RateInfo
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import (
    BaseAdaptor,
)


class RateInfoAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: RateInfoEntity):
        return RateInfo(
            id=domain_entity.id,
            rate_info_id=domain_entity.rate_info_id,
            start_time=domain_entity.start_time,
            rate=domain_entity.rate,
            channel_id=domain_entity.channel_id,
            room_name=domain_entity.room_name,
            room_id=domain_entity.room_id,
            check_in=domain_entity.check_in,
            hotel_id=domain_entity.hotel_id,
            check_out=domain_entity.check_out,
            on_site_rate=domain_entity.on_site_rate,
            net_rate=domain_entity.net_rate,
            currency=domain_entity.currency,
            rate_description=domain_entity.rate_description,
            rate_type=domain_entity.rate_type,
            source_url=domain_entity.source_url,
            max_occupancy=domain_entity.max_occupancy,
            is_promo=domain_entity.is_promo,
            closed=domain_entity.closed,
            discount=domain_entity.discount,
            promo_name=domain_entity.promo_name,
            status_code=domain_entity.status_code,
            tax_status=domain_entity.tax_status,
            tax_type=domain_entity.tax_type,
            tax_amount=domain_entity.tax_amount,
            meal_inclusion_details=domain_entity.meal_inclusion_details,
            meal_inclusion_type=domain_entity.meal_inclusion_type,
            room_amenities=domain_entity.room_amenities,
            meal_plan_code=domain_entity.meal_plan_code,
            mlos=domain_entity.mlos,
            insert_time=domain_entity.insert_time,
            scraping_time=domain_entity.scraping_time,
            is_active=domain_entity.is_active,
        )

    def to_domain_entity(self, db_model: RateInfo):
        return RateInfoEntity(
            id=db_model.id,
            rate_info_id=db_model.rate_info_id,
            start_time=db_model.start_time,
            rate=db_model.rate,
            channel_id=db_model.channel_id,
            room_name=db_model.room_name,
            room_id=db_model.room_id,
            check_in=db_model.check_in,
            hotel_id=db_model.hotel_id,
            check_out=db_model.check_out,
            on_site_rate=db_model.on_site_rate,
            net_rate=db_model.net_rate,
            currency=db_model.currency,
            rate_description=db_model.rate_description,
            rate_type=db_model.rate_type,
            source_url=db_model.source_url,
            max_occupancy=db_model.max_occupancy,
            is_promo=db_model.is_promo,
            closed=db_model.closed,
            discount=db_model.discount,
            promo_name=db_model.promo_name,
            status_code=db_model.status_code,
            tax_status=db_model.tax_status,
            tax_type=db_model.tax_type,
            tax_amount=db_model.tax_amount,
            meal_inclusion_details=db_model.meal_inclusion_details,
            meal_inclusion_type=db_model.meal_inclusion_type,
            room_amenities=db_model.room_amenities,
            meal_plan_code=db_model.meal_plan_code,
            mlos=db_model.mlos,
            insert_time=db_model.insert_time,
            scraping_time=db_model.scraping_time,
            is_active=db_model.is_active,
        )
