from hawkeye.domain.entities.competitive_price_weightage import CompetitivePriceWeightage
from hawkeye.domain.entities.compset_pricing_threshold import CompsetPricingThreshold
from hawkeye.domain.entities.room_type_price import RoomTypePrice
from hawkeye.domain.entities.incremental_price_rule import IncrementalPriceRule
from hawkeye.domain.value_objects.rule_definition import RuleDefinition
from hawkeye.infrastructure.database.models import RoomTypePriceModel
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import (
    BaseAdaptor,
)


class RoomTypePriceAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: RoomTypePrice):
        return RoomTypePriceModel(
            id=domain_entity.id,
            hotel_id=domain_entity.hotel_id,
            room_type=domain_entity.room_type,
            target_date=domain_entity.target_date,
            input_price=domain_entity.input_price,
            final_price=domain_entity.final_price,
            rules=[rule.to_json() for rule in domain_entity.rules],
            is_published=domain_entity.is_published,
            price_trigger_id=domain_entity.price_trigger_id,
            is_skipped=domain_entity.is_skipped,
            occupancy_percentage=domain_entity.occupancy_percentage,
            sku_code=domain_entity.sku_code,
            sku_name=domain_entity.sku_name,
        )

    def to_domain_entity(self, db_model: RoomTypePriceModel) -> RoomTypePrice:
        domain_entity_rules = list()
        for rule in db_model.rules:
            if 'rule_type' in rule:
                domain_entity_rules.append(RuleDefinition.from_json(rule))
            elif 'increased_percentage_threshold' in rule:
                domain_entity_rules.append(CompsetPricingThreshold.from_json(rule))
            elif 'higher_competitive_price_multiplier' in rule:
                domain_entity_rules.append(CompetitivePriceWeightage.from_json(rule))
            else:
                domain_entity_rules.append(IncrementalPriceRule.from_json(rule))

        return RoomTypePrice(
            id=db_model.id,
            hotel_id=db_model.hotel_id,
            room_type=db_model.room_type,
            target_date=db_model.target_date,
            input_price=db_model.input_price,
            final_price=db_model.final_price,
            rules=domain_entity_rules,
            is_published=db_model.is_published,
            price_trigger_id=db_model.price_trigger_id,
            is_skipped=db_model.is_skipped,
            occupancy_percentage=db_model.occupancy_percentage,
            sku_code=db_model.sku_code,
            sku_name=db_model.sku_name,
        )
