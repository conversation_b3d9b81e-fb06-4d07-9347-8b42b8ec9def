from hawkeye.domain.entities.hotel_sku_price_rule import HotelSkuPriceRule
from hawkeye.infrastructure.database.models import HotelSkuPriceRuleModel
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import BaseAdaptor

class HotelSkuPriceRuleAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: HotelSkuPriceRule):
        return HotelSkuPriceRuleModel(
            rule_id=domain_entity.rule_id,
            hotel_id=domain_entity.hotel_id,
            rule_name=domain_entity.rule_name,
            rule_type=domain_entity.rule_type,
            sku_code=domain_entity.sku_code,
            config=domain_entity.config,
            factor=domain_entity.factor,
            sum_factor=domain_entity.sum_factor,
            priority=domain_entity.priority,
            start_price=domain_entity.start_price,
            end_price=domain_entity.end_price,
            is_deleted=domain_entity.is_deleted,
        )

    def to_domain_entity(self, db_model: HotelSkuPriceRuleModel) -> HotelSkuPriceRule:
        return HotelSkuPriceRule(
            rule_id=db_model.rule_id,
            hotel_id=db_model.hotel_id,
            rule_name=db_model.rule_name,
            rule_type=db_model.rule_type,
            sku_code=db_model.sku_code,
            config=db_model.config,
            factor=db_model.factor,
            sum_factor=db_model.sum_factor,
            priority=db_model.priority,
            start_price=db_model.start_price,
            end_price=db_model.end_price,
            is_deleted=db_model.is_deleted,
        )
