from hawkeye.domain.entities.competitive_hotel_room_type_mapping import (
    CompetitiveHotelRoomTypeMapping,
)
from hawkeye.infrastructure.database.models import (
    CompetitiveHotelRoomTypeMapping as CompetitiveHotelRoomTypeMappingModel,
)
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import (
    BaseAdaptor,
)


class CompetitiveHotelRoomTypeMappingAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: CompetitiveHotelRoomTypeMapping):
        return CompetitiveHotelRoomTypeMappingModel(
            id=domain_entity.id,
            competitive_hotel_id=domain_entity.competitive_hotel_id,
            competitive_hotel_room_type=domain_entity.competitive_hotel_room_type,
            internal_hotel_id=domain_entity.internal_hotel_id,
            internal_hotel_room_type=domain_entity.internal_hotel_room_type,
            file_id=domain_entity.file_id,
            is_active=domain_entity.is_active,
        )

    def to_domain_entity(self, db_model: CompetitiveHotelRoomTypeMappingModel):
        return CompetitiveHotelRoomTypeMapping(
            id=db_model.id,
            competitive_hotel_id=db_model.competitive_hotel_id,
            competitive_hotel_room_type=db_model.competitive_hotel_room_type,
            internal_hotel_id=db_model.internal_hotel_id,
            internal_hotel_room_type=db_model.internal_hotel_room_type,
            file_id=db_model.file_id,
            is_active=db_model.is_active,
        )
