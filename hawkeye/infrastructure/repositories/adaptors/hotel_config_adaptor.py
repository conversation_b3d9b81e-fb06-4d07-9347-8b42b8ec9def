from hawkeye.domain.entities.hotel_config import HotelConfig
from hawkeye.infrastructure.database.models import HotelConfigModel
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import (
    BaseAdaptor,
)


class HotelConfigAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: HotelConfig):
        return HotelConfigModel(
            hotel_id=domain_entity.hotel_id,
            is_enabled=domain_entity.is_enabled,
            is_live=domain_entity.is_live,
            is_competitor_price_enabled=domain_entity.is_competitor_price_enabled,
            revenue_poc_emails=domain_entity.revenue_poc_emails,
            file_id=domain_entity.file_id,
        )

    def to_domain_entity(self, db_model: HotelConfigModel):
        return HotelConfig(
            hotel_id=db_model.hotel_id,
            is_enabled=db_model.is_enabled,
            is_live=db_model.is_live,
            is_competitor_price_enabled=db_model.is_competitor_price_enabled,
            revenue_poc_emails=db_model.revenue_poc_emails,
            file_id=db_model.file_id,
        )
