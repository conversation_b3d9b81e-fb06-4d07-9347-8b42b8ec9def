from hawkeye.domain.entities.compset_pricing_threshold import CompsetPricingThreshold
from hawkeye.infrastructure.database.models import (
    CompsetPricingThreshold as CompsetPricingThresholdModel,
)
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import (
    BaseAdaptor,
)


class CompsetPricingThresholdAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: CompsetPricingThreshold):
        return CompsetPricingThresholdModel(
            id=domain_entity.id,
            hotel_id=domain_entity.hotel_id,
            stay_date=domain_entity.stay_date,
            increased_percentage_threshold=domain_entity.increased_percentage_threshold,
            decreased_percentage_threshold=domain_entity.decreased_percentage_threshold,
            normalized_competitive_price_avg=domain_entity.normalized_competitive_price_avg,
            file_id=domain_entity.file_id,
            is_deleted=domain_entity.is_deleted,
        )

    def to_domain_entity(self, db_model: CompsetPricingThresholdModel):
        return CompsetPricingThreshold(
            id=db_model.id,
            hotel_id=db_model.hotel_id,
            stay_date=db_model.stay_date,
            increased_percentage_threshold=db_model.increased_percentage_threshold,
            decreased_percentage_threshold=db_model.decreased_percentage_threshold,
            normalized_competitive_price_avg=db_model.normalized_competitive_price_avg,
            file_id=db_model.file_id,
            is_deleted=db_model.is_deleted,
        )
