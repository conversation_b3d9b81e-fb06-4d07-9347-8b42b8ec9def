from hawkeye.domain.entities.hotel_room_type import HotelRoomType
from hawkeye.infrastructure.database.models import HotelRoomTypeModel
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import (
    BaseAdaptor,
)


class HotelRoomTypeAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: HotelRoomType):
        return HotelRoomTypeModel(
            hotel_id=domain_entity.hotel_id,
            room_type_code=domain_entity.room_type_code,
            room_type_name=domain_entity.room_type_name,
            total_rooms=domain_entity.total_rooms,
            id=domain_entity.id,
            is_base_room_type=domain_entity.is_base_room_type,
            is_active=domain_entity.is_active,
            rack_rate=domain_entity.rack_rate,
            max_adults=domain_entity.max_adults,
        )

    def to_domain_entity(self, db_model: HotelRoomTypeModel) -> HotelRoomType:
        return HotelRoomType(
            id=db_model.id,
            hotel_id=db_model.hotel_id,
            room_type_code=db_model.room_type_code,
            room_type_name=db_model.room_type_name,
            total_rooms=db_model.total_rooms,
            is_base_room_type=db_model.is_base_room_type,
            is_active=db_model.is_active,
            rack_rate=db_model.rack_rate,
            max_adults=db_model.max_adults,
        )
