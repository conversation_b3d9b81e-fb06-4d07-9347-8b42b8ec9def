from hawkeye.domain.entities.price_alert import PriceAlert
from hawkeye.infrastructure.database.models import PriceAlert as PriceAlertModel
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import (
    BaseAdaptor,
)


class PriceAlertAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: PriceAlert):
        return PriceAlertModel(
            id=domain_entity.id,
            hotel_id=domain_entity.hotel_id,
            stay_date=domain_entity.stay_date,
            approver_email=domain_entity.approver_email,
            competitive_price=domain_entity.competitive_price,
            hawkeye_price=domain_entity.hawkeye_price,
            threshold_price=domain_entity.threshold_price,
            suggested_change_percentage=domain_entity.suggested_change_percentage,
            alert_threshold_percentage=domain_entity.alert_threshold_percentage,
            price_trigger_id=domain_entity.price_trigger_id,
            is_expired=domain_entity.is_expired,
        )

    def to_domain_entity(self, db_model: PriceAlertModel):
        return PriceAlert(
            id=db_model.id,
            hotel_id=db_model.hotel_id,
            stay_date=db_model.stay_date,
            approver_email=db_model.approver_email,
            competitive_price=db_model.competitive_price,
            hawkeye_price=db_model.hawkeye_price,
            threshold_price=db_model.threshold_price,
            suggested_change_percentage=db_model.suggested_change_percentage,
            alert_threshold_percentage=db_model.alert_threshold_percentage,
            price_trigger_id=db_model.price_trigger_id,
            is_expired=db_model.is_expired,
        )
