from hawkeye.domain.entities.competitive_price_weightage import (
    CompetitivePriceWeightage,
)
from hawkeye.infrastructure.database.models import (
    CompetitivePriceWeightage as CompetitivePriceWeightageModel,
)
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import (
    BaseAdaptor,
)


class CompetitivePriceWeightageAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: CompetitivePriceWeightage):
        return CompetitivePriceWeightageModel(
            id=domain_entity.id,
            hotel_id=domain_entity.hotel_id,
            occupancy_start=domain_entity.occupancy_start,
            occupancy_end=domain_entity.occupancy_end,
            lower_competitive_price_multiplier=domain_entity.lower_competitive_price_multiplier,
            higher_competitive_price_multiplier=domain_entity.higher_competitive_price_multiplier,
            file_id=domain_entity.file_id,
            is_deleted=domain_entity.is_deleted,
        )

    def to_domain_entity(self, db_model: CompetitivePriceWeightageModel):
        return CompetitivePriceWeightage(
            id=db_model.id,
            hotel_id=db_model.hotel_id,
            occupancy_start=db_model.occupancy_start,
            occupancy_end=db_model.occupancy_end,
            lower_competitive_price_multiplier=db_model.lower_competitive_price_multiplier,
            higher_competitive_price_multiplier=db_model.higher_competitive_price_multiplier,
            file_id=db_model.file_id,
            is_deleted=db_model.is_deleted,
        )
