from hawkeye.domain.entities.competitive_hotel_mapping import CompetitiveHotelMapping
from hawkeye.infrastructure.database.models import (
    CompetitiveHotelMapping as CompetitiveHotelMappingModel,
)
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import (
    BaseAdaptor,
)


class CompetitiveHotelMappingAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: CompetitiveHotelMapping):
        return CompetitiveHotelMappingModel(
            id=domain_entity.id,
            internal_hotel_id=domain_entity.internal_hotel_id,
            competitive_hotel_id=domain_entity.competitive_hotel_id,
            is_active=domain_entity.is_active,
            normalization_factor=domain_entity.normalization_factor,
            file_id=domain_entity.file_id,
        )

    def to_domain_entity(self, db_model: CompetitiveHotelMappingModel):
        return CompetitiveHotelMapping(
            id=db_model.id,
            internal_hotel_id=db_model.internal_hotel_id,
            competitive_hotel_id=db_model.competitive_hotel_id,
            is_active=db_model.is_active,
            normalization_factor=db_model.normalization_factor,
            file_id=db_model.file_id,
        )
