from hawkeye.domain.entities.competitor_hotel_pricing import CompetitorHotelPricing
from hawkeye.infrastructure.database.models import (
    CompetitorHotelPricing as CompetitorHotelPricingModel,
)
from hawkeye.infrastructure.repositories.adaptors.base_db_to_domain_entity_adapter import (
    BaseAdaptor,
)


class CompetitorHotelPricingAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: CompetitorHotelPricing):
        return CompetitorHotelPricingModel(
            id=domain_entity.id,
            room_type=domain_entity.room_type,
            price=domain_entity.price,
            competitor_hotel_id=domain_entity.competitor_hotel_id,
            stay_date=domain_entity.stay_date,
            expires_at=domain_entity.expires_at,
        )

    def to_domain_entity(self, db_model: CompetitorHotelPricingModel):
        return CompetitorHotelPricing(
            id=db_model.id,
            room_type=db_model.room_type,
            price=db_model.price,
            competitor_hotel_id=db_model.competitor_hotel_id,
            stay_date=db_model.stay_date,
            expires_at=db_model.expires_at,
        )
