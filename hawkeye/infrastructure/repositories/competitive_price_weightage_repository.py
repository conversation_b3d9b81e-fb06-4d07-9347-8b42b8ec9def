import logging
from typing import List

from hawkeye.domain.entities.competitive_price_weightage import (
    CompetitivePriceWeightage,
)
from hawkeye.infrastructure.database.models import (
    CompetitivePriceWeightage as CompetitivePriceWeightageModel,
)
from hawkeye.infrastructure.exception import DatabaseError
from hawkeye.infrastructure.repositories.adaptors.competitive_price_weightage_adaptor import (
    CompetitivePriceWeightageAdaptor,
)
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class CompetitivePriceWeightageRepository(BaseRepository):
    _model = CompetitivePriceWeightageModel
    _adaptor = CompetitivePriceWeightageAdaptor()

    def load_all(self, hotel_ids=None):
        price_weightage_models = self.query(self._model)
        if hotel_ids:
            price_weightage_models = price_weightage_models.filter(
                self._model.hotel_id.in_(hotel_ids)
            )
        price_weightage_models = price_weightage_models.all()
        return [self._adaptor.to_domain_entity(price_weightage_model) for price_weightage_model in price_weightage_models]

    def create(self, price_weightage: CompetitivePriceWeightage):
        price_weightage_model = self._adaptor.to_db_entity(price_weightage)
        try:
            self._save(price_weightage_model)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def update(self, price_weightage: CompetitivePriceWeightage):
        price_weightage_model = self._adaptor.to_db_entity(price_weightage)
        try:
            self._update(price_weightage_model)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def load_all_for_update(self, hotel_id):
        price_weightage_models = (
            self.query(self._model)
            .filter(
                self._model.is_deleted == False,
                self._model.hotel_id == hotel_id,
            )
            .all()
        )
        return [
            self._adaptor.to_domain_entity(price_weightage_model)
            for price_weightage_model in price_weightage_models
        ]

    def update_all(self, price_weightages: List[CompetitivePriceWeightageModel]):
        price_weightage_models = [
            self._adaptor.to_db_entity(price_weightage) for price_weightage in price_weightages
        ]
        try:
            price_weightage_models = super()._update_all(price_weightage_models)
            return [
                self._adaptor.to_domain_entity(price_weightage_model)
                for price_weightage_model in price_weightage_models
            ]
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def create_all(self, price_weightages: CompetitivePriceWeightage):
        price_weightage_models = [
            self._adaptor.to_db_entity(price_weightage) for price_weightage in price_weightages
        ]
        try:
            super().create_all(price_weightage_models)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())
