import logging
from typing import List

from hawkeye.domain.entities.compset_pricing_threshold import CompsetPricingThreshold
from hawkeye.infrastructure.database.models import (
    CompsetPricingThreshold as CompsetPricingThresholdModel,
)
from hawkeye.infrastructure.exception import DatabaseError
from hawkeye.infrastructure.repositories.adaptors.compset_pricing_threshold_adaptor import (
    CompsetPricingThresholdAdaptor,
)
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class CompsetPricingThresholdRepository(BaseRepository):
    _model = CompsetPricingThresholdModel
    _adaptor = CompsetPricingThresholdAdaptor()

    def load_all(self, hotel_ids=None):
        compset_pricing_threshold_models = self.query(self._model).filter(self._model.is_deleted == False)
        if hotel_ids:
            compset_pricing_threshold_models = compset_pricing_threshold_models.filter(
                self._model.hotel_id.in_(hotel_ids)
            )
        compset_pricing_threshold_models = compset_pricing_threshold_models.all()
        return [
            self._adaptor.to_domain_entity(compset_pricing_threshold_model)
            for compset_pricing_threshold_model in compset_pricing_threshold_models
        ]

    def load_all_for_hotel(self, hotel_id, stay_dates=None):
        query = self.query(self._model).filter(self._model.is_deleted == False)
        if hotel_id:
            query = query.filter(self._model.hotel_id == hotel_id)
        if stay_dates:
            query = query.filter(self._model.stay_date.in_(stay_dates))
        pricing_threshold_models = query.all()
        return [
            self._adaptor.to_domain_entity(pricing_threshold_model)
            for pricing_threshold_model in pricing_threshold_models
        ]

    def load_for_update(self, hotel_id, stay_date):
        return self._adaptor.to_domain_entity(
            self.get_for_update(
                self._model,
                hotel_id=hotel_id,
                stay_date=stay_date,
                is_deleted=False,
            )
        )

    def create(self, pricing_threshold: CompsetPricingThreshold):
        pricing_threshold_model = self._adaptor.to_db_entity(pricing_threshold)
        try:
            self._save(pricing_threshold_model)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def update(self, pricing_threshold: CompsetPricingThreshold):
        pricing_threshold_model = self._adaptor.to_db_entity(pricing_threshold)
        try:
            self._update(pricing_threshold_model)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def update_all(self, pricing_thresholds: List[CompsetPricingThresholdModel]):
        pricing_threshold_models = [
            self._adaptor.to_db_entity(pricing_threshold)
            for pricing_threshold in pricing_thresholds
        ]
        try:
            super()._update_all(pricing_threshold_models)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())

    def create_all(self, pricing_thresholds: [CompsetPricingThreshold]):
        pricing_thresholds_models = [
            self._adaptor.to_db_entity(pricing_threshold)
            for pricing_threshold in pricing_thresholds
        ]
        try:
            super().create_all(pricing_thresholds_models)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())
