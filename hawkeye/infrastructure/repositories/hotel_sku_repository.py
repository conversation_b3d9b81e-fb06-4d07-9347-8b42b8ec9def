import logging
from typing import List

from hawkeye.domain.entities.hotel_sku import HotelSku
from hawkeye.infrastructure.database.models import HotelSkuModel
from hawkeye.infrastructure.exception import DatabaseError
from hawkeye.infrastructure.repositories.adaptors.hotel_sku_adaptor import (
    HotelSkuAdaptor,
)
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class HotelSkuRepository(BaseRepository):
    _model = HotelSkuModel
    _adaptor = HotelSkuAdaptor()

    def create_all(self, hotel_skus: List[HotelSku]):
        hotel_sku_models = [
            self._adaptor.to_db_entity(domain_entity=hotel_sku) for hotel_sku in hotel_skus
        ]
        try:
            self._save_all(hotel_sku_models)
        except Exception as ex:
            raise DatabaseError(description=str(ex)) from ex

    def load_all(self, hotel_ids=None):
        if hotel_ids:
            hotel_sku_models = self.query(self._model).filter(self._model.is_active == True,
                                                              self._model.hotel_id.in_(hotel_ids)).all()
        else:
            hotel_sku_models = self.query(self._model).filter(self._model.is_active == True).all()
        return [
            self._adaptor.to_domain_entity(hotel_sku_model) for hotel_sku_model in hotel_sku_models
        ]

    def load_all_hotel_skus(self, hotel_id):
        hotel_sku_models = self.query(self._model).filter(self._model.hotel_id == hotel_id).all()
        return [
            self._adaptor.to_domain_entity(hotel_sku_model) for hotel_sku_model in hotel_sku_models
        ]

    def load_for_update(self, sku_codes: list):
        hotel_sku_models = self.query(self._model).filter(self._model.sku_id.in_(sku_codes)).all()
        return [
            self._adaptor.to_domain_entity(hotel_sku_model) for hotel_sku_model in hotel_sku_models
        ]

    def update_all(self, hotel_skus):
        hotel_sku_models = [
            self._adaptor.to_db_entity(domain_entity=hotel_sku) for hotel_sku in hotel_skus
        ]
        try:
            self._update_all(hotel_sku_models)
        except Exception as ex:
            raise DatabaseError(description=str(ex)) from ex
