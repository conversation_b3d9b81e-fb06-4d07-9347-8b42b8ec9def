import logging
from collections import defaultdict
from typing import List
from sqlalchemy import func, case
from treebo_commons.utils import dateutils

from hawkeye.domain.entities.room_type_price import RoomTypePrice
from hawkeye.infrastructure.database.models import RoomTypePriceModel
from hawkeye.infrastructure.exception import DatabaseError
from hawkeye.infrastructure.repositories.adaptors.room_type_price_adaptor import RoomTypePriceAdaptor
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class RoomTypePriceRepository(BaseRepository):
    _model = RoomTypePriceModel
    _adaptor = RoomTypePriceAdaptor()

    def save_all(self, room_type_prices: List[RoomTypePrice]):
        start_time = dateutils.current_datetime()
        room_type_price_models = [
            self._adaptor.to_db_entity(room_type_price) for room_type_price in room_type_prices
        ]
        logger.info(f"Time taken to build models {(dateutils.current_datetime() - start_time).seconds}")
        start_time = dateutils.current_datetime()
        try:
            self._save_all(room_type_price_models)
        except Exception as ex:
            raise DatabaseError(description=ex.__str__())
        logger.info(
            f"Time taken to save {len(room_type_price_models)}prices {(dateutils.current_datetime() - start_time).seconds}"
        )

    def bulk_save_or_update(self, room_type_prices: List[RoomTypePrice]):
        room_type_price_models = [
            self._adaptor.to_db_entity(room_type_price) for room_type_price in room_type_prices
        ]
        try:
            self._bulk_save_or_update(room_type_price_models)
        except Exception as exp:
            raise DatabaseError(description=exp.__str__())

    def bulk_update_mappings(self, room_type_prices: List[RoomTypePrice]):
        room_type_price_models = [
            self._adaptor.to_db_entity(room_type_price) for room_type_price in room_type_prices
        ]
        room_type_price_mappings = [
            room_type_price_model.mapping_dict() for room_type_price_model in room_type_price_models
        ]
        try:
            self._bulk_update_mappings(self._model, room_type_price_mappings)
        except Exception as exp:
            raise DatabaseError(description=exp.__str__())

    def update_all(self, room_type_prices: List[RoomTypePrice]):
        room_type_price_models = [
            self._adaptor.to_db_entity(room_type_price) for room_type_price in room_type_prices
        ]
        try:
            self._update_all(room_type_price_models)
        except Exception as exp:
            raise DatabaseError(description=exp.__str__())

    def load_bulk_for_update(self, hotel_id, dates):
        q = self.filter(self._model, for_update=True)
        q = q.filter(self._model.hotel_id==hotel_id, self._model.target_date.in_(dates))
        q = q.filter(self._model.is_published.is_(False))
        room_type_price_models = q.all()
        return [self._adaptor.to_domain_entity(model) for model in room_type_price_models]

    def load_bulk_for_re_publish(self, hotel_id, start_date, end_date):
        subquery = (
            self.session().query(func.max(self._model.id), )
            .filter(self._model.hotel_id == hotel_id)
            .filter(self._model.target_date >= start_date)
            .filter(self._model.target_date <= end_date)
            .group_by(self._model.target_date, self._model.sku_name)
        )
        q = self.filter(self._model, for_update=True)
        q = q.filter(self._model.id.in_(subquery))
        room_type_price_models = q.all()
        return [self._adaptor.to_domain_entity(model) for model in room_type_price_models]

    def get_unpublished_hotel_target_dates(self):
        hotel_target_dates = self.session().query(
            self._model.hotel_id, self._model.target_date
        ).filter(
            self._model.is_published.is_(False),
        ).order_by(self._model.hotel_id, self._model.target_date).distinct()
        return [(row[0], row[1]) for row in hotel_target_dates]

    def load_room_price_for_date_range(self, hotel_id, start_date, end_date):
        subquery = (
            self.session().query(
                func.max(self._model.id))
            .filter(self._model.hotel_id == hotel_id)
            .filter(self._model.room_type.isnot(None))
            .filter(self._model.target_date >= start_date)
            .filter(self._model.target_date <= end_date)
            .group_by(self._model.target_date, self._model.sku_name)
        )
        q = self.filter(self._model)
        room_type_price_models = q.filter(self._model.id.in_(subquery)).all()

        room_type_price_date_wise = defaultdict(dict)
        for model in room_type_price_models:
            domain_entity = self._adaptor.to_domain_entity(model)
            room_type_price_date_wise[f'{domain_entity.target_date}_{domain_entity.sku_name}'] = domain_entity.final_price

        return room_type_price_date_wise

    def load_room_price_for_sku_codes(self, hotel_id, sku_codes, start_date, end_date):
        subquery = (
            self.session().query(
                func.max(self._model.id))
            .filter(self._model.hotel_id == hotel_id)
            .filter(self._model.target_date >= start_date)
            .filter(self._model.target_date <= end_date)
            .filter(self._model.sku_code.in_(sku_codes))
            .group_by(self._model.target_date, self._model.sku_code)
        )
        q = self.filter(self._model)
        room_type_price_models = q.filter(self._model.id.in_(subquery)).all()
        return room_type_price_models

    def load_sku_prices_for_date_range(self, hotel_id, start_date, end_date, sku_codes):
        sku_price_models = self.load_room_price_for_sku_codes(
            hotel_id, sku_codes, start_date, end_date
        )
        sku_price_date_wise = defaultdict(dict)
        for model in sku_price_models:
            domain_entity = self._adaptor.to_domain_entity(model)
            sku_price_date_wise[domain_entity.target_date][domain_entity.sku_name] = domain_entity.final_price

        return sku_price_date_wise
