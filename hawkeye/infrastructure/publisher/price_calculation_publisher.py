import logging

from treebo_commons.multitenancy.tenant_client import TenantClient

from hawkeye.infrastructure.publisher.publisher_config import PriceCalculationPublisherConfig
from hawkeye.infrastructure.publisher.queue_service import BaseQueueService
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class PriceCalculationPublisher(BaseQueueService):
    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        super().__init__(PriceCalculationPublisherConfig(tenant_id))

    def publish(self, price_calculation_event):
        self._publish(price_calculation_event.dict())
        logger.info(f"Price calculation triggered for: {price_calculation_event.dict()}")
