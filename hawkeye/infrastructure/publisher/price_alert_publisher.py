import logging

from treebo_commons.multitenancy.tenant_client import TenantClient

from hawkeye.infrastructure.publisher.publisher_config import PriceAlertConfig
from hawkeye.infrastructure.publisher.queue_service import BaseQueueService
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class PriceAlertPublisher(BaseQueueService):
    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        super().__init__(PriceAlertConfig(tenant_id))

    def publish(self, price_alert):
        self._publish(price_alert.to_dict())
        logger.info(f"Sending price alert : {price_alert.to_dict()}")
