import os
import logging
from typing import Optional

from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.psycopg2 import Psycopg2Instrumentor
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.resources import Resource, SERVICE_NAME, SERVICE_VERSION
from opentelemetry.propagate import set_global_textmap
from opentelemetry.propagators.b3 import B3MultiFormat

logger = logging.getLogger(__name__)


class OpenTelemetryConfig:
    """OpenTelemetry configuration for Hawkeye application."""

    def __init__(self):
        self.service_name = os.environ.get('OTEL_SERVICE_NAME', 'hawkeye')
        self.service_version = os.environ.get('OTEL_SERVICE_VERSION', '1.8.0')
        self.environment = os.environ.get('APP_ENV', 'local')
        self.tempo_endpoint = os.environ.get('OTEL_EXPORTER_OTLP_ENDPOINT', 'http://localhost:4317')
        self.enabled = os.environ.get('OTEL_ENABLED', 'true').lower() == 'true'
        self.sample_rate = float(os.environ.get('OTEL_TRACE_SAMPLE_RATE', '1.0'))

    def initialize_tracing(self) -> Optional[trace.Tracer]:
        """Initialize OpenTelemetry tracing with Grafana Tempo."""
        if not self.enabled:
            logger.info("OpenTelemetry tracing is disabled")
            return None

        try:
            # Create resource with service information
            resource = Resource.create({
                SERVICE_NAME: self.service_name,
                SERVICE_VERSION: self.service_version,
                "environment": self.environment,
                "aws.region": os.environ.get('AWS_REGION', 'unknown'),
                "cluster.identifier": os.environ.get('CLUSTER_IDENTIFIER', 'unknown'),
            })

            # Create tracer provider
            tracer_provider = TracerProvider(resource=resource)

            # Create OTLP exporter for Grafana Tempo
            otlp_exporter = OTLPSpanExporter(
                endpoint=self.tempo_endpoint,
                insecure=True,  # Use insecure for local development
            )

            # Create span processor
            span_processor = BatchSpanProcessor(otlp_exporter)
            tracer_provider.add_span_processor(span_processor)

            # Set the global tracer provider
            trace.set_tracer_provider(tracer_provider)

            # Set up propagators for distributed tracing
            set_global_textmap(B3MultiFormat())

            logger.info(f"OpenTelemetry tracing initialized for service: {self.service_name}")
            logger.info(f"Tempo endpoint: {self.tempo_endpoint}")

            return trace.get_tracer(__name__)

        except Exception as e:
            logger.error(f"Failed to initialize OpenTelemetry tracing: {e}")
            return None

    def instrument_libraries(self, app=None):
        """Instrument common libraries for automatic tracing."""
        if not self.enabled:
            return

        try:
            # Instrument FastAPI
            if app:
                FastAPIInstrumentor.instrument_app(app)
                logger.info("FastAPI instrumentation enabled")

            # Instrument SQLAlchemy
            SQLAlchemyInstrumentor().instrument()
            logger.info("SQLAlchemy instrumentation enabled")

            # Instrument Redis
            RedisInstrumentor().instrument()
            logger.info("Redis instrumentation enabled")

            # Instrument Requests
            RequestsInstrumentor().instrument()
            logger.info("Requests instrumentation enabled")

            # Instrument Psycopg2
            Psycopg2Instrumentor().instrument()
            logger.info("Psycopg2 instrumentation enabled")

        except Exception as e:
            logger.error(f"Failed to instrument libraries: {e}")


def initialize_telemetry(app=None) -> Optional[trace.Tracer]:
    """Initialize OpenTelemetry for the Hawkeye application."""
    config = OpenTelemetryConfig()
    tracer = config.initialize_tracing()
    config.instrument_libraries(app)
    return tracer
