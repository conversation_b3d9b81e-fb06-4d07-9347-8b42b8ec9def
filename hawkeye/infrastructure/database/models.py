from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Boolean,
    Column,
    Date,
    DateTime,
    Float,
    Integer,
    String,
    TEXT,
    func, Index, UniqueConstraint, DECIMAL, BigInteger
)
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class TimeStampMixin(object):
    """
    model for created time and modified time
    """

    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    modified_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )


class PriceUpdateConfigModel(Base, TimeStampMixin):
    __tablename__ = "price_update_config"

    id = Column("id", Integer, primary_key=True, autoincrement=True)
    hour = Column("hour", Integer, nullable=False)
    minute = Column("minute", Integer, nullable=False)
    abw_start = Column("abw_start", Integer, nullable=False)
    abw_end = Column("abw_end", Integer, nullable=False)
    is_active = Column("is_active", Boolean, default=True)

    __table_args__ = (
        Index('ix_price_config_hour_minute', 'hour', 'minute', postgresql_where=(is_active)),
    )


class BaseRoomTypePriceRuleModel(Base, TimeStampMixin):
    __tablename__ = "base_room_type_price_rule"

    rule_id = Column("rule_id", Integer, primary_key=True, autoincrement=True)
    hotel_id = Column("hotel_id", String, nullable=False)
    rule_name = Column("rule_name", String)
    rule_type = Column("rule_type", String, nullable=False)
    config = Column("config", JSON)
    is_input_price = Column("is_input_price", Boolean, default=False)
    factor = Column("factor", DECIMAL(precision=15, scale=4), default=0.0)
    sum_factor = Column("sum_factor", DECIMAL(precision=15, scale=4), default=0.0)
    priority = Column("priority", Integer, default=1)
    start_price = Column("start_price", DECIMAL(precision=15, scale=2))
    end_price = Column("end_price", DECIMAL(precision=15, scale=2))
    is_deleted = Column("is_deleted", Boolean, default=False)
    file_id = Column("file_id", Integer, nullable=True)

    __table_args__ = (
        Index('ix_room_type_price_rule_hotel_created', 'hotel_id', 'created_at', postgresql_where=(~is_deleted)),
        Index('ix_room_type_price_rule_created', 'created_at', postgresql_where=(~is_deleted)),
    )


class RuleFileUploadModel(Base, object):
    __tablename__ = "rule_file_upload"

    id = Column("id", Integer, primary_key=True, autoincrement=True)
    file_name = Column("file_name", String)
    path = Column("path", String)
    uploaded_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    status = Column(String)
    modified_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )
    uploaded_by = Column(String(255))


class HotelConfigModel(Base, TimeStampMixin):
    __tablename__ = "hotel_config"

    hotel_id = Column("hotel_id", String, primary_key=True)
    is_enabled = Column("is_enabled", Boolean, default=False)
    is_live = Column("is_live", Boolean, default=True)
    is_competitor_price_enabled = Column(Boolean, default=False)
    revenue_poc_emails = Column(TEXT, nullable=True)
    file_id = Column("file_id", Integer, nullable=True)

    __table_args__ = (
        Index('ix_hotel_live_enabled', 'is_live', 'is_enabled'),
    )


class HotelRoomInventoryModel(Base, TimeStampMixin):
    __tablename__ = "hotel_room_inventory"

    hotel_id = Column("hotel_id", String, primary_key=True)
    room_type = Column("room_type", String, primary_key=True)
    date = Column("date", Date, primary_key=True)
    out_of_order = Column("out_of_order", Integer)
    out_of_service = Column("out_of_service", Integer)
    availability_count = Column("availability_count", Integer)

    __table_args__ = (
        Index('ix_inventory_hotel_date_room_type', 'hotel_id', 'date', 'room_type'),
    )


class RoomTypePriceModel(Base, TimeStampMixin):
    __tablename__ = "room_type_price"

    id = Column("id", Integer, primary_key=True, autoincrement=True)
    hotel_id = Column("hotel_id", String, nullable=False)
    room_type = Column("room_type", String)
    target_date = Column("target_date", Date)
    input_price = Column("input_price", Float)
    final_price = Column("final_price", Float)
    rules = Column("config", JSON)
    is_published = Column("is_published", Boolean, default=False)
    occupancy_percentage = Column("occupancy_percentage", DECIMAL(precision=5, scale=2), default=0.0)
    price_trigger_id = Column("price_trigger_id", String)
    is_skipped = Column("is_skipped", Boolean, default=False)
    sku_code = Column("sku_code", String)
    sku_name = Column("sku_name", String)

    __table_args__ = (
        Index('ix_unpublished_price_hotel_date', 'hotel_id', 'target_date', postgresql_where=(~is_published)),
    )

    def mapping_dict(self):
        return {
            "id": self.id,
            "hotel_id": self.hotel_id,
            "room_type": self.room_type,
            "target_date": self.target_date,
            "input_price": self.input_price,
            "final_price": self.final_price,
            "config": self.rules,
            "is_published": self.is_published,
            "occupancy_percentage": self.occupancy_percentage,
            "price_trigger_id": self.price_trigger_id,
            "is_skipped": self.is_skipped,
            "sku_code": self.sku_code,
            "sku_name": self.sku_name,
        }


class HotelRoomTypeModel(Base, TimeStampMixin):
    __tablename__ = "hotel_room_type"

    id = Column("id", Integer, primary_key=True, autoincrement=True)
    hotel_id = Column("hotel_id", String, nullable=False)
    room_type_code = Column("room_type_code", String, nullable=False)
    room_type_name = Column("room_type_name", String, nullable=False)
    total_rooms = Column("total_rooms", Integer)
    is_base_room_type = Column("is_base_room_type", Boolean, default=False)
    is_active = Column("is_active", Boolean, default=True)
    rack_rate = Column("rack_rate", DECIMAL(precision=15, scale=4))
    max_adults = Column("max_adults", Integer, nullable=True)

    __table_args__ = (
        Index('idx_hrt_hotel_id_room_type_code', 'hotel_id', 'room_type_code'),
        Index('idx_hrt_hotel_id_is_active', 'hotel_id', 'is_active'),
        UniqueConstraint(
            'hotel_id', 'room_type_code', name='ix_hrt_hotel_id_room_type_code_uniq'
        ),
    )


class IncrementalPriceRuleModel(Base, TimeStampMixin):
    __tablename__ = "incremental_price_rule"

    rule_id = Column("rule_id", Integer, primary_key=True, autoincrement=True)
    hotel_id = Column("hotel_id", String, nullable=False)
    room_type = Column("room_type_code", String, nullable=False)
    factor = Column("factor", Float, default=1)
    sum_factor = Column("sum_factor", Float, default=0.0)
    is_deleted = Column("is_deleted", Boolean, default=False)

    __table_args__ = (
        Index('ix_room_type_price_rule_hotel', 'is_deleted', 'hotel_id'),
    )


class HotelSkuModel(Base, TimeStampMixin):
    __tablename__ = "hotel_sku"

    id = Column("id", Integer, primary_key=True, autoincrement=True)
    hotel_id = Column("hotel_id", String, nullable=False)
    sku_code = Column("sku_code", String, nullable=False)
    sku_category = Column("sku_category", String)
    rack_rate = Column("rack_rate", DECIMAL(precision=15, scale=4))
    description = Column("description", TEXT)
    extra_information = Column("extra_information", JSON)
    sku_name = Column("sku_name", String, nullable=False)
    is_active = Column(Boolean, default=True)

    __table_args__ = (
        Index('idx_hst_hotel_id_sku_code', 'hotel_id', 'sku_code'),
        Index('ix_hotel_sku', 'is_active', 'hotel_id'),
        UniqueConstraint(
            'hotel_id', 'sku_code', name='ix_hst_hotel_id_sku_code_uniq'
        ),
        UniqueConstraint(
            'hotel_id', 'sku_name', name='ix_hst_hotel_id_sku_name_uniq'
        ),
    )


class BaseRoomTypeSkuPriceRuleModel(Base, TimeStampMixin):
    __tablename__ = "base_room_type_sku_price_rule"

    rule_id = Column("rule_id", Integer, primary_key=True, autoincrement=True)
    hotel_id = Column("hotel_id", String, nullable=False)
    sku_code = Column("sku_code", String, nullable=False)
    input_type = Column("input_type", String, nullable=True)
    rule_name = Column("rule_name", String)
    rule_type = Column("rule_type", String, nullable=False)
    config = Column("config", JSON)
    factor = Column("factor", DECIMAL(precision=15, scale=4), default=0.0)
    sum_factor = Column("sum_factor", DECIMAL(precision=15, scale=4), default=0.0)
    priority = Column("priority", Integer, default=1)
    start_price = Column("start_price", DECIMAL(precision=15, scale=2))
    end_price = Column("end_price", DECIMAL(precision=15, scale=2))
    is_deleted = Column("is_deleted", Boolean, default=False)
    file_id = Column("file_id", Integer, nullable=True)

    __table_args__ = (
        Index('ix_sku_pricing_rule_hotel_created', 'hotel_id', 'created_at', postgresql_where=(~is_deleted)),
        Index('ix_sku_pricing_rule_created', 'created_at', postgresql_where=(~is_deleted)),
    )


class User(Base, TimeStampMixin):
    __tablename__ = 'admin_user'

    id = Column(Integer, primary_key=True)
    email = Column(String(255), unique=True, nullable=False)
    password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)


class PriceCalculationTriggerModel(Base, TimeStampMixin):
    __tablename__ = "price_calculation_trigger"

    id = Column(String, primary_key=True)
    source = Column(String(255), nullable=False)
    price_update_config_id = Column("price_update_config_id", String)
    user_email = Column("user_email", String(255))
    abw_start = Column("abw_start", Integer)
    abw_end = Column("abw_end", Integer)
    hotel_ids = Column("hotel_ids", String)


class CompetitorHotelPricing(Base, TimeStampMixin):
    __tablename__ = "competitor_hotel_pricing"

    id = Column(Integer, primary_key=True, autoincrement=True)
    competitor_hotel_id = Column(String(100), nullable=False)
    stay_date = Column(Date, nullable=False)
    room_type = Column(String(500), nullable=False)
    price = Column(Float, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)

    __table_args__ = (
        Index("ix_chp_competitor_hotel_id_stay_date", "competitor_hotel_id", "stay_date"),
    )


class CompsetPricingThreshold(Base, TimeStampMixin):
    __tablename__ = "compset_pricing_threshold"

    id = Column(Integer, primary_key=True, autoincrement=True)
    hotel_id = Column(String(10), nullable=False, index=True)
    stay_date = Column(Date, nullable=False)
    increased_percentage_threshold = Column(Float, nullable=False)
    decreased_percentage_threshold = Column(Float, nullable=False)
    normalized_competitive_price_avg = Column(Float)
    file_id = Column(Integer, nullable=True)
    is_deleted = Column(Boolean, default=False)

    __table_args__ = (Index("ix_cpt_hotel_id_stay_date", "hotel_id", "stay_date"),)


class PriceAlert(Base, TimeStampMixin):
    __tablename__ = "price_alert"

    id = Column(Integer, primary_key=True, autoincrement=True)
    hotel_id = Column(String(10), nullable=False)
    stay_date = Column(Date, nullable=False)
    approver_email = Column(TEXT, nullable=True)
    competitive_price = Column(Float, nullable=False)
    hawkeye_price = Column(Float, nullable=False)
    threshold_price = Column(Float, nullable=False)
    suggested_change_percentage = Column(Float, nullable=False)
    alert_threshold_percentage = Column(Float, nullable=False)
    price_trigger_id = Column("price_trigger_id", String)
    is_expired = Column(Boolean, default=False)

    __table_args__ = (
        Index("ix_pa_hotel_id_stay_date", "hotel_id", "stay_date"),
    )


class CompetitivePriceWeightage(Base, TimeStampMixin):
    __tablename__ = "competitive_price_weightage"

    id = Column(Integer, primary_key=True, autoincrement=True)
    hotel_id = Column(String(10), nullable=False)
    occupancy_start = Column(Float, nullable=False)
    occupancy_end = Column(Float, nullable=False)
    lower_competitive_price_multiplier = Column(Float, nullable=False)
    higher_competitive_price_multiplier = Column(Float, nullable=False)
    file_id = Column(Integer, nullable=True)
    is_deleted = Column(Boolean, default=False)

    __table_args__ = (
        Index("ix_cpw_hotel_id", "hotel_id"),
    )


class CompetitiveHotelMapping(Base, TimeStampMixin):
    __tablename__ = "competitive_hotel_mapping"

    id = Column(Integer, primary_key=True, autoincrement=True)
    internal_hotel_id = Column(String(10), nullable=False)
    competitive_hotel_id = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=False)
    normalization_factor = Column(Float, nullable=False)
    file_id = Column(Integer, nullable=True)

    __table_args__ = (
        Index("ix_chm_internal_hotel_competitive_hotel", "internal_hotel_id", "competitive_hotel_id"),
        Index("ix_chm_hotel_competitive_active_mappings", "internal_hotel_id", "is_active"),
        UniqueConstraint(
            "internal_hotel_id",
            "competitive_hotel_id",
            name="uq_chm_internal_hotel_id_competitive_hotel_id",
        ),
    )


class CompetitiveHotelRoomTypeMapping(Base, TimeStampMixin):
    __tablename__ = "competitive_hotel_room_type_mappings"

    id = Column(Integer, primary_key=True, index=True)
    competitive_hotel_id = Column(String, nullable=False)
    competitive_hotel_room_type = Column(String, nullable=False)
    internal_hotel_id = Column(String, nullable=False)
    internal_hotel_room_type = Column(String, nullable=False)
    file_id = Column(Integer, nullable=True)
    is_active = Column(Boolean, nullable=False, default=True)


class RateInfo(Base, TimeStampMixin):
    __tablename__ = 'rate_info'
    id = Column(Integer, primary_key=True, autoincrement=True)
    rate_info_id = Column(BigInteger, nullable=False)
    start_time = Column(DateTime, nullable=False)
    rate = Column(Float, nullable=False, default=0)
    channel_id = Column(Integer, nullable=False)
    room_name = Column(TEXT, nullable=False)
    room_id = Column(Integer, nullable=False)
    check_in = Column(DateTime, nullable=False)
    hotel_id = Column(Integer, nullable=True)
    check_out = Column(DateTime, nullable=False)
    on_site_rate = Column(Float, nullable=False)
    net_rate = Column(Float, nullable=False)
    currency = Column(String(10), nullable=False)
    rate_description = Column(TEXT, nullable=False)
    rate_type = Column(String(255), nullable=False)
    source_url = Column(TEXT, nullable=False)
    max_occupancy = Column(Integer, nullable=False)
    is_promo = Column(String(1), nullable=False)
    closed = Column(String(1), nullable=False)
    discount = Column(Float, nullable=False)
    promo_name = Column(TEXT, nullable=False)
    status_code = Column(Integer, nullable=False)
    tax_status = Column(Integer, nullable=False)
    tax_type = Column(String(150), nullable=False)
    tax_amount = Column(Float, nullable=False)
    meal_inclusion_details = Column(TEXT, nullable=False)
    meal_inclusion_type = Column(TEXT, nullable=False)
    room_amenities = Column(TEXT, nullable=False)
    meal_plan_code = Column(Integer, nullable=False, default=0)
    mlos = Column(Integer, nullable=False, default=1)
    insert_time = Column(DateTime, nullable=False)
    scraping_time = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=False)

    __table_args__ = (
        Index("ix_rate_info_hotel_id_checkin_active", "hotel_id", "check_in", "is_active"),
        Index("ix_rate_info_hotel_id_active_rates", "hotel_id", postgresql_where=(is_active == True)),
    )
