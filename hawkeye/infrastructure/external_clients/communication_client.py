from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from object_registry import register_instance
from hawkeye.infrastructure.external_clients.core.base_extenal_client import BaseExternalClient
from hawkeye.infrastructure.external_clients.service_registry import ServiceRegistryClient


@register_instance()
class CommunicationServiceClient(BaseExternalClient):
    page_map = {
        'send_communication': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/communication/api/v2/send",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_communication_service_url()

    def get_headers(self):
        headers = {"referrer": "/", "content-type": "application/json"}
        enrich_outgoing_request(headers)

    def send_email(
        self,
        identifier: str,
        context_data: dict,
        to_emails: list,
        subject: str,
        reply_to: str = None,
        from_email: str = None,
        attachments: list = None,
    ):
        page_name = "send_communication"
        post_date = dict(
            identifier=identifier,
            context_data=context_data,
            email=dict(
                to_emails=to_emails,
                reply_to=reply_to,
                from_email=from_email,
                subject=subject,
                attachments=attachments if attachments else [],
            ),
        )

        response = self.make_call(page_name, data=post_date)
        if not response.is_success():
            raise Exception(
                "Communication API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response
