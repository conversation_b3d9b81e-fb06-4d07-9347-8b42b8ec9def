import enum
import os
from treebo_commons.service_discovery.service_registry import ServiceRegistry


class ServiceEndPointNames(enum.Enum):
    CATALOG_SERVICE_URL = "catalog_service_url"
    CRS_SERVICE_URL = "crs_service_url"
    COMMUNICATION_SERVICE_URL = "communication_service_url"


class ServiceRegistryClient:
    ALL_API_ENDPOINTS = ServiceRegistry.get_all_service_endpoints()

    @classmethod
    def get_catalog_service_url(cls):
        service_name = ServiceEndPointNames.CATALOG_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_crs_service_url(cls):
        service_name = ServiceEndPointNames.CRS_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_communication_service_url(cls):
        service_name = ServiceEndPointNames.COMMUNICATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))
