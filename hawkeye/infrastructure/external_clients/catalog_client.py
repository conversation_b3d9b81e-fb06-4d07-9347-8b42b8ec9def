import json
import logging

from hawkeye.constants.hawkeye_constant import CATALOG_HOTEL_DETAILS_CACHE_EXPIRY
from hawkeye.infrastructure.cache import cache
from hawkeye.infrastructure.exception import CatalogClientException
from hawkeye.infrastructure.external_clients.core.base_extenal_client import (
    BaseExternalClient,
)
from hawkeye.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class CatalogServiceClient(BaseExternalClient):
    page_map = {
        "fetch_hotel": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v1/properties/{hotel_id}",
        ),
        "get_all_hotels": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/api/v3/properties/?fields={fields}&per_page={per_page}&page={page}",
        ),
        "get_skus": dict(
            type=BaseExternalClient.CallTypes.GET, url_regex="/cataloging-service/api/v1/sku/?"
        ),
        "get_rooms": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/cataloging-service/api/v1/properties/{hotel_id}/rooms",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_catalog_service_url()

    def fetch_hotel(self, hotel_id):
        page_name = "fetch_hotel"
        url_params = dict(hotel_id=hotel_id)
        cache_key = f"fetch_hotel:{hotel_id}"
        cached_data = cache.get_from_cache(cache_key)
        if cached_data:
            return json.loads(cached_data)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise CatalogClientException(
                description="Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        hotel_data = response.json_response
        cache.set_in_cache(
            key=cache_key, value=json.dumps(hotel_data), expiry=CATALOG_HOTEL_DETAILS_CACHE_EXPIRY
        )
        return hotel_data

    def get_skus(self, sku_codes=None):
        page_name = "get_skus"
        url_params = dict()
        if sku_codes:
            url_params["codes"] = sku_codes
        response = self.make_call(page_name, optional_url_params=url_params)
        if not response.is_success():
            raise CatalogClientException(
                description="Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response

    def _get_all_hotels_api(self, page=1):
        catalog_hotel_list = []
        page_name = "get_all_hotels"
        url_params = dict(fields="id,status", per_page=200, page=page)
        response = self.make_call(page_name, url_parameters=url_params)
        if response.is_success():
            response_json = response.json_response
            has_next = response_json["meta"]["pagination"]["has_next"]
            next_page = response_json["meta"]["pagination"]["next_page"]
            for hotel_data in response_json["data"]:
                catalog_hotel_list.append(hotel_data)
            return catalog_hotel_list, has_next, next_page
        else:
            raise CatalogClientException(
                description="Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

    def get_all_hotels(self):
        aggregate_hotel_data = []
        try:
            has_next = True
            next_page = 1
            while has_next:
                hotel_data, has_next, next_page = self._get_all_hotels_api(next_page)
                if hotel_data:
                    aggregate_hotel_data.extend(hotel_data)
        except CatalogClientException as ex:
            error_message = "Fetching hotel_data from catalog failed because of {}".format(ex)
            logger.error(error_message)
        return aggregate_hotel_data

    def get_rooms_information(self, hotel_id):
        page_name = "get_rooms"
        url_params = dict(hotel_id=hotel_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise CatalogClientException(
                description="Catalog API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )

        return response.json_response
