import logging

from hawkeye.infrastructure.exception import CrsClientException
from hawkeye.infrastructure.external_clients.core.base_extenal_client import BaseExternalClient
from hawkeye.infrastructure.external_clients.service_registry import ServiceRegistryClient
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class CrsClient(BaseExternalClient):
    page_map = {
        'get_dnr': dict(type=BaseExternalClient.CallTypes.GET,
                        url_regex="/v1/hotels/{hotel_id}/dnrs?from_date={from_date}&to_date={to_date}"),
        'get_hotel_configs': dict(type=BaseExternalClient.CallTypes.GET,
                                  url_regex="/v1/hotel-configs"),
        'get_room_type_inventory': dict(type=BaseExternalClient.CallTypes.GET,
                                        url_regex="/v1/hotels/{hotel_id}/room-type-inventories?from_date={from_date}&to_date={to_date}")
    }

    def get_domain(self):
        return ServiceRegistryClient.get_crs_service_url()

    def get_dnr_information(self, hotel_id, from_date, to_date):
        page_name = 'get_dnr'
        url_parameters = {
            'hotel_id': hotel_id,
            'from_date': from_date,
            'to_date': to_date,
        }

        response = self.make_call(page_name, url_parameters=url_parameters)
        if response.is_success():
            return response.json_response['data']

        raise CrsClientException(description=
                                 "CRS API Error. Status Code: {0}, Errors: {1}".format(response.response_code,
                                                                                       response.errors))

    def get_room_type_inventories(self, hotel_id, from_date, to_date):
        page_name = 'get_room_type_inventory'
        url_parameters = {
            'hotel_id': hotel_id,
            'from_date': from_date,
            'to_date': to_date,
        }

        response = self.make_call(page_name, url_parameters=url_parameters)
        if response.is_success():
            return response.json_response['data']

        raise CrsClientException(description=
                                 "CRS API Error. Status Code: {0}, Errors: {1}".format(response.response_code,
                                                                                       response.errors))

    def get_hotel_ids(self):
        page_name = 'get_hotel_configs'
        response = self.make_call(page_name)
        if not response.is_success():
            raise Exception(
                "CRS API Error. Status Code: {0}, Errors: {1}".format(response.response_code,
                                                                          response.errors))
        response = response.json_response
        hotel_configs = response.get('data', {})
        hotel_ids = [hotel_config.get('hotel_id') for hotel_config in hotel_configs]
        return hotel_ids
