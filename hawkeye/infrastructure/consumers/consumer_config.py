from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient


class CatalogConfig(object):
    """
    Catalog Message Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "cs_exchange"
        self.exchange_type = "topic"
        self.queue_name = "catalog-event-queue"
        self.routing_keys = [
            "com.cs.property",
            "com.cs.property.room.type.config",
            'com.cs.roomrackrate'
        ]
        self.exclusive = False


class CrsConsumerConfig(object):
    """
    Crs Message Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "crs_exchange"
        self.exchange_type = "topic"
        self.queue_name = "crs-event-queue"
        self.routing_keys = [
            'inventory.dnr.removed',
            'inventory.dnr.set',
            'inventory.room_type.updated',
            'inventory.dnr.updated',
            'inventory.dnr.released',
            'housekeeping_record.updated',
        ]
        self.exclusive = False


class PriceCalculationConsumerConfig(object):
    """
    Price Calculation Message Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = 'hawkeye-price-calculation'
        self.exchange_type = 'topic'
        self.queue_name = "price-calculation-event-queue"
        self.routing_keys = [
            'com.hawkeye.prices',
        ]
        self.exclusive = False


class PriceAlertConsumerConfig(object):
    """
    Price Alert Message Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = 'hawkeye-price-alert'
        self.exchange_type = 'topic'
        self.queue_name = "hawkeye-price-alert-queue"
        self.routing_keys = [
            'com.hawkeye.alerts',
        ]
        self.exclusive = False
