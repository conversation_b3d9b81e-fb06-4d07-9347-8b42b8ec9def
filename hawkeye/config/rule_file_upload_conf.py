from collections import namedtuple

PriceRuleParams = namedtuple(
    'PriceRuleParams',
    [
        'hotel_id',
        'rule_type',
        'start_range',
        'end_range',
        'multiplier',
        'addition',
        'start_price',
        'end_price',
        'sku',
        'input_type',
    ]
)

CSV_COLUMNS = PriceRuleParams(
    'hotel_id',
    'rule_type',
    'start_range',
    'end_range',
    'multiplier',
    'addition',
    'start_price',
    'end_price',
    'sku',
    'input_type',
)
