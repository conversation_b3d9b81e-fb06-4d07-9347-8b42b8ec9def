import logging
import unittest
import os

from app.core.logging import configure_logging


class TestLogging(unittest.TestCase):
    def setUp(self):
        # Save original handlers to restore after test
        self.root_logger = logging.getLogger()
        self.original_handlers = list(self.root_logger.handlers)
        self.original_level = self.root_logger.level
    
    def tearDown(self):
        # Restore original logging configuration
        self.root_logger.handlers = self.original_handlers
        self.root_logger.setLevel(self.original_level)
    
    def test_configure_logging_debug(self):
        # Call the function with DEBUG level
        configure_logging("DEBUG")
        
        # Verify log level was set to DEBUG
        self.assertEqual(self.root_logger.level, logging.DEBUG)
        
        # Verify at least one handler exists
        self.assertTrue(len(self.root_logger.handlers) > 0)
        
        # Verify the handler is a StreamHandler
        self.assertTrue(any(isinstance(h, logging.StreamHandler) for h in self.root_logger.handlers))
    
    def test_configure_logging_info(self):
        # Call the function with INFO level
        configure_logging("INFO")
        
        # Verify log level was set to INFO
        self.assertEqual(self.root_logger.level, logging.INFO)
    
    def test_logger_levels(self):
        # Configure logging with INFO level
        configure_logging("INFO")
        
        # Check that app logger is set to INFO
        app_logger = logging.getLogger("app")
        self.assertEqual(app_logger.level, logging.INFO)
        
        # Check that a module logger inherits the level
        module_logger = logging.getLogger("app.integrations.wifi.levotel")
        self.assertEqual(module_logger.getEffectiveLevel(), logging.INFO)
    
    def test_env_variable_log_level(self):
        # Save original environment variable if it exists
        original_log_level = os.environ.get("LOG_LEVEL")
        
        try:
            # Set environment variable
            os.environ["LOG_LEVEL"] = "WARNING"
            
            # Configure logging without explicit level
            configure_logging()
            
            # Verify log level was set to WARNING
            self.assertEqual(self.root_logger.level, logging.WARNING)
        finally:
            # Restore original environment variable
            if original_log_level:
                os.environ["LOG_LEVEL"] = original_log_level
            else:
                os.environ.pop("LOG_LEVEL", None)


if __name__ == "__main__":
    unittest.main()
