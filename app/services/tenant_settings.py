import json

from app.constants import TenantConfigName, DEFAULT_LEVOTEL_WIFI_LOGIN_CONFIG
from app.infrastructure.external_clients.catalog_dispatcher import CatalogDispatcher


class TenantSettings:
    def __init__(self):
        self.catalog_client = CatalogDispatcher()

    async def _get_tenant_config(self, hotel_id=None):
        hotel_configs = await self.catalog_client.get_tenant_configs(hotel_id)
        return {config['config_name']: config['config_value'] for config in hotel_configs}

    async def get_levotel_wifi_login_config(self, hotel_id=None):
        """
        Fetch the Levotel Wi-Fi login URL for a given hotel.
        """
        hotel_config = await self._get_tenant_config(hotel_id)
        levotel_wifi_config = json.loads(hotel_config.get(TenantConfigName.LEVOTEL_WIFI_CONFIG.value))
        if (
            levotel_wifi_config
            and "wifi_name" in levotel_wifi_config
            and "login_url_host" in levotel_wifi_config
        ):
            return levotel_wifi_config
        return DEFAULT_LEVOTEL_WIFI_LOGIN_CONFIG
