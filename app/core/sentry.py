import os

import sentry_sdk
from sentry_sdk.integrations.aws_lambda import AwsLambdaIntegration


def init_sentry():
    sentry_sdk.init(
        dsn=os.environ.get("SENTRY_DSN"),
        environment=os.environ.get("SERVICE_NAME", "joker"),
        integrations=[AwsLambdaIntegration(timeout_warning=True)],
        traces_sample_rate=0.1,  # Adjust for performance tracing (0 to disable)
        shutdown_timeout=2.0,  # seconds
    )
