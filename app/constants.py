from app.utils.base_enum import BaseEnum

APP_NAME = 'Joker'
VENDOR_CREDENTIALS_SECRET_NAME = (
    "{tenant_id}/apps/"
    + APP_NAME
    + "/providers/{hardware_provider}/interfaces/{interface_type}/credentials"
)


class InterfaceType(BaseEnum):
    WIFI = 'wifi'


class HardwareProvider(BaseEnum):
    LEVOTEL = 'levotel'


class Network(BaseEnum):
    LAN = 'LAN'
    CLOUD = 'CLOUD'


class CACHE_KEYS(BaseEnum):
    INTERFACES = "interfaces:{property_id}"
    VENDOR_CREDENTIALS = (
        "providers:{hardware_provider}:interfaces:{interface_type}:credentials"
    )


class PushMessageType(BaseEnum):
    GUEST_CHECKIN = "GUEST_CHECKIN"
    SECONDARY_GUEST_CHECKIN = "SECONDARY_GUEST_CHECKIN"
    GUEST_CHECKOUT = "GUEST_CHECKOUT"
    SECONDARY_GUEST_CHECKOUT = "SECONDARY_GUEST_CHECKOUT"
    GUEST_ROOM_CHANGE = "GUEST_ROOM_CHANGE"
    SECONDARY_GUEST_ROOM_CHANGE = "SECONDARY_GUEST_ROOM_CHANGE"
    GUEST_NAME_UPDATE = "GUEST_NAME_UPDATE"
    SECONDARY_GUEST_NAME_UPDATE = "SECONDARY_GUEST_NAME_UPDATE"


class CommunicationIdentifier(BaseEnum):
    LEVOTEL_WIFI_CREDENTIALS = "levotel_wifi_credentials"
    LEVOTEL_UPDATED_WIFI_CREDENTIALS = "levotel_updated_wifi_credentials"


class TenantConfigName(BaseEnum):
    LEVOTEL_WIFI_CONFIG = "levotel_wifi_config"


DEFAULT_LEVOTEL_WIFI_LOGIN_CONFIG = {
    "wifi_name": "Treebo Club",
    "login_url_host": "https://treebo.levotelwifi.com/login",
}
