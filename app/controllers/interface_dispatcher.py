import asyncio
import json
import logging
from typing import Dict, List

import sentry_sdk
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.credentials.exceptions import GetSecretError

from app.constants import (
    CACHE_KEYS,
    VENDOR_CREDENTIALS_SECRET_NAME,
    HardwareProvider,
    InterfaceType,
)
from app.controllers.dispatcher import Dispatcher<PERSON><PERSON>
from app.integrations.wifi.levotel import LevotelWifiIntegration
from app.services.cache import cache
from app.utils.request_context import app_context

logger = logging.getLogger(__name__)


class InterfaceDispatcher(DispatcherBase):

    def get_interface_integration(self, interface: Dict, interface_config: Dict):
        if interface.get("interface_type") == InterfaceType.WIFI.value:
            if interface.get("hardware_provider") == HardwareProvider.LEVOTEL.value:
                return LevotelWifiIntegration(interface, interface_config)
            else:
                raise ValueError(
                    f"Unsupported hardware provider: {interface.get('hardware_provider')}"
                )
        else:
            raise ValueError(
                f"Unsupported interface type: {interface.get('interface_type')}"
            )

    async def dispatch(self, event: Dict, interfaces: List[Dict]):
        if not interfaces:
            logger.warning(
                f"No interfaces found for property: {app_context.get_property_id()}"
            )
            return

        # Dynamically invoke matching integrations
        tasks = []
        for interface in interfaces:
            cache_key = CACHE_KEYS.VENDOR_CREDENTIALS.value.format(
                hardware_provider=interface["hardware_provider"]
                .replace(" ", "_")
                .lower(),
                interface_type=interface["interface_type"].replace(" ", "_").lower(),
            )
            config = await cache.get(cache_key)
            if not config:
                try:
                    config = AwsSecretManager.get_secret_by_name(
                        VENDOR_CREDENTIALS_SECRET_NAME.format(
                            tenant_id=app_context.get_tenant_id(),
                            hardware_provider=interface["hardware_provider"]
                            .replace(" ", "_")
                            .lower(),
                            interface_type=interface["interface_type"]
                            .replace(" ", "_")
                            .lower(),
                        )
                    )
                except GetSecretError as e:
                    # Uncomment this to test locally
                    # config = {
                    #     "api_key": "test",
                    #     "base_url": "https://webhook.site/c8ed7ccb-7dd4-4a41-82ee-9b0eb46f8b5e#"
                    # }
                    logger.error(f"No config found for interface: {interface}")
                    continue
                await cache.set(cache_key, json.dumps(config))
            else:
                config = json.loads(config)
            interface_integration = self.get_interface_integration(interface, config)

            tasks.append(interface_integration.handle_pms_message(event))

        if not tasks:
            logger.warning(
                f"No matching interface integrations found for message_type: {event.get('method')}"
            )
            return

        results = await asyncio.gather(*tasks, return_exceptions=True)

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                sentry_sdk.capture_exception(result)
                print(f"Task {i} failed: {result}")
            else:
                print(f"Task {i} result: {result}")
