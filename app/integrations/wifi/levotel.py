import json
import logging
import sentry_sdk

from typing import Dict

from app.constants import PushMessageType, CommunicationIdentifier
from app.exceptions import LevotelIntegrationException
from app.infrastructure.external_clients.communication_dispatcher import CommunicationDispatcher
from app.integrations.base_interface_integration import BaseInterfaceIntegration
from app.services.tenant_settings import TenantSettings
from app.utils.request_context import app_context

logger = logging.getLogger(__name__)


class LevotelWifiIntegration(BaseInterfaceIntegration):

    def get_dispatch_config(self, message_type: str) -> tuple:
        """
        Returns HTTP method and URL to be used based on message_type.
        """
        if message_type == PushMessageType.GUEST_CHECKIN.value:
            return "GET", f"{self.config['base_url']}/api/"
        elif message_type == PushMessageType.GUEST_CHECKOUT.value:
            return "GET", f"{self.config['base_url']}/api/"
        else:
            logger.info(f"Unsupported message_type for Levotel: {message_type}")

    def _parse_response_content(self, response_data) -> dict:
        """
        Parse response content handling both JSON and text content types.

        Args:
            response_data: Response data from HTTP client

        Returns:
            dict: Parsed response data
        """
        if isinstance(response_data, dict):
            return response_data
        elif isinstance(response_data, str):
            try:
                return json.loads(response_data)
            except json.JSONDecodeError:
                logger.error(f"Failed to parse response as JSON: {response_data}")
                return {}
        else:
            logger.error(f"Unexpected response type: {type(response_data)}")
            return {}

    def _check_for_failure_response(self, parsed_response: dict, operation: str):
        """
        Check if the response indicates a failure and raise exception if needed.

        Args:
            parsed_response: Parsed response data
            operation: The operation being performed (checkin/checkout)
        """
        # Check for failure in Checkin or Checkout operations
        # Try both capitalized and lowercase versions to handle different response formats
        operation_result = None

        if operation.lower() in parsed_response:
            operation_key = operation.lower()
            operation_result = parsed_response.get(operation_key)
        elif operation.capitalize() in parsed_response:
            operation_key = operation.capitalize()
            operation_result = parsed_response.get(operation_key)

        if operation_result != "Failure":
            return

        error_message = parsed_response.get("Message", "Unknown error")

        # Create exception
        exception = LevotelIntegrationException(
            operation=operation,
            message=error_message,
            response_data=parsed_response
        )

        # Check if this should be ignored
        if exception.should_be_ignored():
            logger.info(f"Ignoring Levotel {operation} failure: {error_message}")
            return

        logger.warning(f"Levotel {operation} failed: {error_message}, Response: {parsed_response}")
        raise exception

    async def send_message_to_vendor(self, message_type: str, payload: dict):
        """
        Send message to vendor and handle error responses.
        """
        http_method, url = self.get_dispatch_config(message_type)
        request_func = getattr(self.http_client, http_method.lower())
        response = await request_func(url, params=payload)

        # Parse the response content
        parsed_response = self._parse_response_content(response)

        # Determine operation type from payload
        operation = payload.get("op", "").capitalize()
        if operation:
            self._check_for_failure_response(parsed_response, operation)

        return response

    async def handle_guest_checkin(self, payload: dict) -> dict:
        """
        Encodes payload for CHECKIN message for Levotel and sends it to vendor.
        """
        checkin_payload = {
            "op": "checkin",
            "apikey": self.config["levotel_hotel_id"],
            "RN": payload["room_number"],
            "GN": payload["guest_last_name"] or payload["guest_first_name"],
        }
        await self.send_message_to_vendor(PushMessageType.GUEST_CHECKIN.value, checkin_payload)

    async def handle_guest_checkout(self, payload: dict) -> dict:
        """
        Encodes payload for CHECKOUT message for Levotel and sends it to vendor.
        """
        checkout_payload = {
            "op": "checkout",
            "apikey": self.config["levotel_hotel_id"],
            "RN": payload["room_number"],
            "GN": payload["guest_last_name"] or payload["guest_first_name"],
        }
        await self.send_message_to_vendor(PushMessageType.GUEST_CHECKOUT.value, checkout_payload)

    async def handle_guest_room_change(self, payload: dict):
        """
        Encodes payload for GUEST_ROOM_CHANGE message for Levotel and sends it to vendor.
        """
        checkout_payload = {
            "op": "checkout",
            "apikey": self.config["levotel_hotel_id"],
            "RN": payload["old_room_number"],
            "GN": payload["guest_last_name"] or payload["guest_first_name"],
        }
        try:
            await self.send_message_to_vendor(PushMessageType.GUEST_CHECKOUT.value, checkout_payload)
        except LevotelIntegrationException as ex:
            # Ignore checkout failure if guest was not checked in
            pass

        # Proceed with checkin regardless of checkout success
        checkin_payload = {
            "op": "checkin",
            "apikey": self.config["levotel_hotel_id"],
            "RN": payload["room_number"],
            "GN": payload["guest_last_name"] or payload["guest_first_name"],
        }
        await self.send_message_to_vendor(PushMessageType.GUEST_CHECKIN.value, checkin_payload)

    async def handle_guest_name_update(self, payload: dict):
        """
        Encodes payload for GUEST_NAME_UPDATE message for Levotel and sends it to vendor.
        """
        checkout_payload = {
            "op": "checkout",
            "apikey": self.config["levotel_hotel_id"],
            "RN": payload["room_number"],
            "GN": payload["old_guest_last_name"] or payload["old_guest_first_name"],
        }
        try:
            await self.send_message_to_vendor(PushMessageType.GUEST_CHECKOUT.value, checkout_payload)
        except LevotelIntegrationException as ex:
            # Ignore checkout failure if guest was not checked in
            pass

        # Proceed with checkin regardless of checkout success
        checkin_payload = {
            "op": "checkin",
            "apikey": self.config["levotel_hotel_id"],
            "RN": payload["room_number"],
            "GN": payload["guest_last_name"] or payload["guest_first_name"],
        }
        await self.send_message_to_vendor(PushMessageType.GUEST_CHECKIN.value, checkin_payload)

    async def handle_secondary_guest_checkin(self, event: Dict) -> Dict | None:
        """
        Encodes payload for SECONDARY_GUEST_CHECKIN message for Levotel and sends it to vendor.
        """
        await self.handle_guest_checkin(event)

    async def handle_secondary_guest_checkout(self, event: Dict) -> Dict | None:
        """
        Encodes payload for SECONDARY_GUEST_CHECKOUT message for Levotel and sends it to vendor.
        """
        await self.handle_guest_checkout(event)

    async def handle_secondary_guest_room_change(self, event: Dict) -> Dict | None:
        """
        Encodes payload for SECONDARY_GUEST_ROOM_CHANGE message for Levotel and sends it to vendor.
        """
        await self.handle_guest_room_change(event)

    async def handle_secondary_guest_name_update(self, event: Dict) -> Dict | None:
        """
        Encodes payload for SECONDARY_GUEST_NAME_UPDATE message for Levotel and sends it to vendor.
        """
        await self.handle_guest_name_update(event)

    async def handle_pms_message(self, payload: dict):
        """
        Do appropriate action based on message_type.
        """
        message_type = payload["method"]

        payload["guest_first_name"], payload["guest_last_name"] = self.remove_additional_names(
            payload["guest_first_name"], payload["guest_last_name"]
        )

        if message_type == PushMessageType.GUEST_CHECKIN.value:
            await self.handle_guest_checkin(payload)
        elif message_type == PushMessageType.GUEST_CHECKOUT.value:
            await self.handle_guest_checkout(payload)
        elif message_type == PushMessageType.GUEST_ROOM_CHANGE.value:
            await self.handle_guest_room_change(payload)
        elif message_type == PushMessageType.GUEST_NAME_UPDATE.value:
            await self.handle_guest_name_update(payload)
        elif message_type == PushMessageType.SECONDARY_GUEST_CHECKIN.value:
            await self.handle_secondary_guest_checkin(payload)
        elif message_type == PushMessageType.SECONDARY_GUEST_CHECKOUT.value:
            await self.handle_secondary_guest_checkout(payload)
        elif message_type == PushMessageType.SECONDARY_GUEST_ROOM_CHANGE.value:
            await self.handle_secondary_guest_room_change(payload)
        elif message_type == PushMessageType.SECONDARY_GUEST_NAME_UPDATE.value:
            await self.handle_secondary_guest_name_update(payload)
        else:
            logger.info(f"Unsupported message_type for Levotel: {message_type}")
            return

        await self.send_guest_communication(payload)

    async def send_guest_communication(self, payload: dict) -> None:
        """
        Sends guest communication about Wi-Fi creds.
        """
        message_type = payload["method"]
        if (message_type not in PushMessageType.all() or
            message_type in [PushMessageType.GUEST_CHECKOUT.value, PushMessageType.SECONDARY_GUEST_CHECKOUT.value]
        ):
            logger.info(f"Message type not eligible for guest communication {message_type}")
            return
        if not payload.get("guest_phone_number"):
            logger.info(f"No phone number provided for guest communication. {payload}")
            return
        is_update_event = bool(
            message_type in [
                PushMessageType.GUEST_NAME_UPDATE.value, PushMessageType.GUEST_ROOM_CHANGE.value,
                PushMessageType.SECONDARY_GUEST_NAME_UPDATE.value, PushMessageType.SECONDARY_GUEST_ROOM_CHANGE.value
            ]
        )
        login_config = await TenantSettings().get_levotel_wifi_login_config(app_context.get_property_id())
        login_url = login_config["login_url_host"] + "?user={username}&password={password}"
        context = dict(
            username=payload["room_number"],
            password=payload["guest_last_name"] or payload["guest_first_name"],
            wifi_name=login_config["wifi_name"],
            login_link=login_url.format(
                username=payload["room_number"],
                password=payload["guest_last_name"] or payload["guest_first_name"]
            ),
        )
        identifier = CommunicationIdentifier.LEVOTEL_WIFI_CREDENTIALS.value if not is_update_event else (
            CommunicationIdentifier.LEVOTEL_UPDATED_WIFI_CREDENTIALS.value)
        receivers = f"{payload.get('country_code') or ''}{payload.get('guest_phone_number')}"
        try:
            await CommunicationDispatcher().send_sms_or_whatsapp(
                identifier=identifier, context_data=context, receivers=[receivers]
            )
            logger.info(f"Sent guest communication for Wi-Fi creds: {context} to {receivers}")
        except Exception as ex:
            logger.info(f"Failed to send guest communication for Wi-Fi creds: {context} to {receivers}. Error: {ex}")

    @staticmethod
    def remove_additional_names(first_name: str, last_name: str) -> tuple[str, str]:
        first_name = first_name.strip().split()[0] if first_name else ""
        last_name = last_name.strip().split()[-1] if last_name else ""
        return first_name, last_name
