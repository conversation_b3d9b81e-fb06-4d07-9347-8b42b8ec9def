import copy
import logging
import os
import time
import threading
from contextlib import contextmanager

from treebo_commons.request_tracing import get_request_id

logger = logging.getLogger(__name__)


class RequestContext(threading.local):
    def __init__(self):
        super(RequestContext, self).__init__()

    def clear(self):
        self.__dict__.clear()


request_context = RequestContext()


def setup_request_context_from_request_headers(request_headers):
    request_context.start_time = time.time()
    request_context.request_id = get_request_id(request_headers)
    # all `HTTP_` alternative header value are checked to support <PERSON><PERSON><PERSON>'s request.META headers
    if 'X-Application' in request_headers or 'HTTP_X_APPLICATION' in request_headers:
        request_context.application = request_headers.get("X-Application") or request_headers.get("HTTP_X_APPLICATION")
    elif 'Client' in request_headers or 'HTTP_CLIENT' in request_headers:
        # used by Gupshup client
        request_context.application = request_headers.get("Client") or request_headers.get("HTTP_CLIENT")

    if 'X-User' in request_headers or 'HTTP_X_USER' in request_headers:
        request_context.user = request_headers.get("X-User") or request_headers.get("HTTP_X_USER")
    if 'X-User-Type' in request_headers or 'HTTP_X_USER_TYPE' in request_headers:
        request_context.user_type = request_headers.get("X-User-Type") or request_headers.get("HTTP_X_USER_TYPE")
    if 'X-Auth-Id' in request_headers or 'HTTP_X_AUTH_ID' in request_headers:
        request_context.auth_id = request_headers.get("X-Auth-Id") or request_headers.get("HTTP_X_AUTH_ID")
    if 'X-UPS-Id' in request_headers or 'HTTP_X_UPS_ID' in request_headers:
        request_context.ups_id = request_headers.get("X-UPS-Id") or request_headers.get("HTTP_X_UPS_ID")
    if 'X-THSC-Version' in request_headers or 'HTTP_X_THSC_VERSION' in request_headers:
        request_context.thsc_version = request_headers.get("X-THSC-Version") or request_headers.get(
            "HTTP_X_THSC_VERSION")

    request_context.tenant_id = request_headers.get("X-Tenant-Id") or request_headers.get("HTTP_X_TENANT_ID")
    request_context.hotel_id = request_headers.get("X-Hotel-Id") or request_headers.get("HTTP_X_HOTEL_ID")
    request_context.application_trace = ApplicationSourceTrace.build_from_request_scope(request_headers)


def set_app_timezone(timezone):
    import pytz
    if isinstance(timezone, str):
        request_context.app_timezone = pytz.timezone(timezone)
    elif hasattr(timezone, 'zone'):
        request_context.app_timezone = timezone
    else:
        raise ValueError(
            "Unable to identify the timezone passed. Please pass timezone value as 'string' or 'pytz' object")


def get_app_timezone():
    return getattr(request_context, 'app_timezone', None)


def get_current_request_id():
    return getattr(request_context, 'request_id', None)


def get_current_tenant_id():
    return getattr(request_context, 'tenant_id', None)


def get_current_hotel_id():
    return getattr(request_context, 'hotel_id', None)


def get_current_application_trace(as_string=False):
    application_trace = getattr(request_context, 'application_trace', None)

    if not application_trace:
        application = getattr(request_context, 'application', None)
        application_trace = ApplicationSourceTrace.from_string(application) if application else None

    if as_string:
        return str(application_trace) if application_trace else None

    return application_trace


class ApplicationSourceTrace:

    TRACE_DELIMITER = "->"

    def __init__(self, trace):
        self.trace = trace

    @classmethod
    def build_from_request_scope(cls, request_headers):
        application_trace = (
            request_headers.get("X-Forwarded-Application") or request_headers.get("HTTP_X_FORWARDED_APPLICATION")
        )
        if not application_trace and hasattr(request_context, 'application'):
            application_trace = request_context.application

        if not application_trace:
            return None
        return ApplicationSourceTrace(trace=application_trace.split(cls.TRACE_DELIMITER))

    @staticmethod
    def build_outgoing_request_trace():
        current_trace = get_current_application_trace()
        current_app = os.environ.get('APP_NAME')
        if not current_trace:
            return current_app
        out_going_request_trace = copy.deepcopy(current_trace)
        out_going_request_trace.add_trace(current_app or 'UNKNOWN')
        return str(out_going_request_trace)

    def __str__(self):
        return self.TRACE_DELIMITER.join(self.trace)

    def add_trace(self, app_name):
        self.trace.append(app_name)

    @classmethod
    def from_string(cls, trace_string):
        return ApplicationSourceTrace(trace=trace_string.split(cls.TRACE_DELIMITER))

    @property
    def origin_app(self):
        return self.trace[0] if self.trace else None

    @property
    def upstream(self):
        return self.trace[-1] if self.trace else None


@contextmanager
def patched_application_trace(trace_string):
    # Helper method to override the current application trace.
    # Example: The payment link success callback service can use this method to replace the existing
    # application trace with the stored trace received during the payment link generation request.
    original_trace = get_current_application_trace()
    if trace_string:
        request_context.application_trace = ApplicationSourceTrace.from_string(trace_string)

    try:
        yield
    finally:
        request_context.application_trace = original_trace
