{"pagination": {"DescribeTapeArchives": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "output_token": "<PERSON><PERSON>", "result_key": "TapeArchives"}, "DescribeTapeRecoveryPoints": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "output_token": "<PERSON><PERSON>", "result_key": "TapeRecoveryPointInfos"}, "DescribeTapes": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "output_token": "<PERSON><PERSON>", "result_key": "Tapes"}, "DescribeVTLDevices": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "output_token": "<PERSON><PERSON>", "result_key": "VTLDevices"}, "ListGateways": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "output_token": "<PERSON><PERSON>", "result_key": "Gateways"}, "ListVolumes": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "output_token": "<PERSON><PERSON>", "result_key": "VolumeInfos"}, "ListTapes": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "output_token": "<PERSON><PERSON>", "result_key": "TapeInfos"}, "ListFileShares": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "non_aggregate_keys": ["<PERSON><PERSON>"], "output_token": "NextMarker", "result_key": "FileShareInfoList"}, "ListTagsForResource": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "non_aggregate_keys": ["ResourceARN"], "output_token": "<PERSON><PERSON>", "result_key": "Tags"}, "ListTapePools": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "output_token": "<PERSON><PERSON>", "result_key": "PoolInfos"}, "ListFileSystemAssociations": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "non_aggregate_keys": ["<PERSON><PERSON>"], "output_token": "NextMarker", "result_key": "FileSystemAssociationSummaryList"}, "ListCacheReports": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "result_key": "CacheReportList"}}}