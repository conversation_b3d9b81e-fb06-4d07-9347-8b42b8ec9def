{"pagination": {"ListEnvironmentAccountConnections": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "environmentAccountConnections"}, "ListEnvironmentTemplateVersions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "templateVersions"}, "ListEnvironmentTemplates": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "templates"}, "ListEnvironments": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "environments"}, "ListServiceInstances": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "serviceInstances"}, "ListServiceTemplateVersions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "templateVersions"}, "ListServiceTemplates": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "templates"}, "ListServices": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "services"}, "ListTagsForResource": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "tags"}, "ListEnvironmentOutputs": {"input_token": "nextToken", "output_token": "nextToken", "result_key": "outputs"}, "ListEnvironmentProvisionedResources": {"input_token": "nextToken", "output_token": "nextToken", "result_key": "provisionedResources"}, "ListRepositories": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "repositories"}, "ListRepositorySyncDefinitions": {"input_token": "nextToken", "output_token": "nextToken", "result_key": "syncDefinitions"}, "ListServiceInstanceOutputs": {"input_token": "nextToken", "output_token": "nextToken", "result_key": "outputs"}, "ListServiceInstanceProvisionedResources": {"input_token": "nextToken", "output_token": "nextToken", "result_key": "provisionedResources"}, "ListServicePipelineOutputs": {"input_token": "nextToken", "output_token": "nextToken", "result_key": "outputs"}, "ListServicePipelineProvisionedResources": {"input_token": "nextToken", "output_token": "nextToken", "result_key": "provisionedResources"}, "ListComponentOutputs": {"input_token": "nextToken", "output_token": "nextToken", "result_key": "outputs"}, "ListComponentProvisionedResources": {"input_token": "nextToken", "output_token": "nextToken", "result_key": "provisionedResources"}, "ListComponents": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "components"}, "ListDeployments": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "deployments"}}}