# ---------------------------------------------------------------------------
#
# This file configures OpenTelemetry for Grafana Tempo integration.
#
# OpenTelemetry is an observability framework for cloud-native software.
# This configuration replaces New Relic with Grafana Tempo for distributed tracing.
#
# For more information on OpenTelemetry configuration, see:
# https://opentelemetry.io/docs/
#
# For Grafana Tempo documentation, see:
# https://grafana.com/docs/tempo/
#
# ---------------------------------------------------------------------------

# Here are the settings that are common to all environments.

[opentelemetry]

# Service name - this identifies your application in Grafana Tempo
service_name = Tenant Gateway

# Service version - helps track deployments
service_version = 1.8.0

# Enable or disable OpenTelemetry tracing
# Set to false to disable tracing entirely
enabled = true

# Grafana Tempo endpoint for OTLP traces
# For local development, this might be http://localhost:4317
# For production, this should be your Tempo OTLP gRPC endpoint
tempo_endpoint = http://localhost:4317

# Trace sampling rate (0.0 to 1.0)
# 1.0 = sample all traces, 0.1 = sample 10% of traces
# Adjust based on your traffic volume and storage capacity
trace_sample_rate = 1.0

# Enable automatic instrumentation for common libraries
# These will automatically create spans for database queries, HTTP requests, etc.
auto_instrument_flask = true
auto_instrument_sqlalchemy = true
auto_instrument_redis = true
auto_instrument_requests = true
auto_instrument_psycopg2 = true

# Resource attributes - additional metadata attached to all spans
# These help identify the service instance and environment
[opentelemetry.resource_attributes]
environment = development
aws_region = us-east-1
cluster_identifier = local-dev

# ---------------------------------------------------------------------------
#
# Environment-specific configurations
# These override the common settings above for specific environments
#

[opentelemetry:development]
enabled = true
trace_sample_rate = 1.0
tempo_endpoint = http://localhost:4317

[opentelemetry:test]
enabled = false
trace_sample_rate = 0.0

[opentelemetry:staging]
enabled = true
trace_sample_rate = 0.5
tempo_endpoint = https://tempo-staging.your-domain.com:4317

[opentelemetry:production]
enabled = true
trace_sample_rate = 0.1
tempo_endpoint = https://tempo.your-domain.com:4317

# ---------------------------------------------------------------------------
#
# Instrumentation settings
# Configure specific instrumentation behavior
#

[opentelemetry.instrumentation]

# Flask instrumentation settings
flask_capture_headers = false
flask_capture_request_body = false
flask_capture_response_body = false

# SQLAlchemy instrumentation settings
sqlalchemy_capture_statement_params = false
sqlalchemy_capture_query_plans = false

# Redis instrumentation settings
redis_capture_statement = true

# HTTP requests instrumentation settings
requests_capture_headers = false
requests_capture_request_body = false
requests_capture_response_body = false

# ---------------------------------------------------------------------------
#
# Span processors and exporters configuration
#

[opentelemetry.processors]

# Batch span processor settings
# These control how spans are batched and sent to Tempo
batch_timeout_ms = 5000
batch_max_export_size = 512
batch_export_timeout_ms = 30000
batch_max_queue_size = 2048

# ---------------------------------------------------------------------------
#
# Logging configuration for OpenTelemetry
#

[opentelemetry.logging]

# Log level for OpenTelemetry internal logs
# Options: DEBUG, INFO, WARNING, ERROR, CRITICAL
log_level = INFO

# Whether to log span exports (useful for debugging)
log_span_exports = false

# ---------------------------------------------------------------------------
