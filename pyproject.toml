[tool.black]
line-length = 88
skip-string-normalization = true
safe = true
exclude = '''
/(
      .git
    | .hg
    | .mypy_cache
    | .tox
    | .venv
    | logs
    | .idea
    | migrations
    | tenant_gateway/integration_tests
)/
'''
force-exclude='''
/(
    tenant_gateway/integration_tests
)/
'''

[tool.isort]
profile = 'black'
multi_line_output = 3
skip = ["__init__.py"]

[tool.pdm]
distribution = false

[project]
name = "tenant_gateway"
version = "0.1.0"
description = "Default template for PDM package"
authors = [
    {name = "<PERSON><PERSON>av", email = "<EMAIL>"},
]
dependencies = ["Flask==2.3.3", "SQLAlchemy==2.0.20", "Flask-SQLAlchemy==3.1.1", "requests==2.31.0", "python-dateutil==2.8.2", "click==8.1.7", "Flask-Caching==2.0.2", "Flask-HTTPAuth==4.8.0", "apispec==6.3.0", "apispec-webframeworks==0.5.2", "PyJWT==2.8.0", "paramiko==2.7.2", "pydantic==2.3.0", "kombu==5.3.2", "marshmallow==3.20.1", "marshmallow-enum==1.5.1", "psycopg2==2.9.7", "psycogreen==1.0.2", "phonenumbers==8.10.17", "redis==5.0.0", "sentry-sdk[flask]>=2.29.1", "thsc==2.2.51", "treebo-commons==3.2.5", "flaskhealthcheck==1.4.3"]
requires-python = "==3.11.*"
readme = "README.md"
license = {text = "MIT"}

