[tool.black]
line-length = 99
safe = true
exclude = '''
/(
      .git
    | .hg
    | .mypy_cache
    | .tox
    | .venv
    | logs
    | .idea
)/
'''

[tool.isort]
profile = 'black'
multi_line_output = 3

[tool.poetry]
name = "Superhero Hawkeye"
version = "1.8.0"
description = ""
authors = ["<PERSON><PERSON> <<EMAIL>>"]
package-mode = false

[[tool.poetry.source]]
name = "treebo-commons"
url = "https://pypi.tree.bo/simple"
priority = "supplemental"

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.82.0"
uvicorn = {extras = ["standard"], version = "^0.18.3"}
treebo-commons = {version = "3.1.0", source = "treebo-commons"}
dacite = "^1.6.0"
aenum = "^3.1.11"
openpyxl = "*"
kombu = "5.2.4"  # Exact version to prevent upgrades
amqp = "5.2.0"  # Exact version to prevent upgrades
fastapi-health = "^0.4.0"
FastAPI-SQLAlchemy = "^0.2.1"
sqlalchemy = "^1.4.42"
requests = "^2.19.1"
psycopg2 = "^2.9.3"
sentry-sdk = {extras = ["fastapi"], version = "^1.9.8"}
logstash_formatter = "^0.5.17"
redis = "^2.10.6"
sqladmin = "^0.20.1"
pytz = "^2023.3.post1"
itsdangerous = "^2.2.0"
opentelemetry-api = "^1.34.1"
opentelemetry-sdk = "^1.34.1"
opentelemetry-exporter-otlp-proto-grpc = "^1.34.1"
opentelemetry-instrumentation-fastapi = "^0.55b1"
opentelemetry-instrumentation-sqlalchemy = "^0.55b1"
opentelemetry-instrumentation-redis = "^0.55b1"
opentelemetry-instrumentation-requests = "^0.55b1"
opentelemetry-instrumentation-psycopg2 = "^0.55b1"
opentelemetry-propagator-b3 = "^1.34.1"

[tool.poetry.group.dev.dependencies]
black = "^22.8.0"
pre-commit = "^2.20.0"
isort = "^5.10.1"
pytest = "^7.1.3"
pylint = "^2.15.2"
autoflake = "^1.5"
mypy = "^0.971"
requests = "^2.28.1"
