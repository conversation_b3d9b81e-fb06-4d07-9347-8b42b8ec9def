import functools
import logging
from typing import Optional, Callable, Any

from opentelemetry import trace
from opentelemetry.trace import Status, StatusCode

logger = logging.getLogger(__name__)


def background_task(name: Optional[str] = None, **kwargs) -> Callable:
    """
    OpenTelemetry replacement for newrelic.agent.background_task decorator.
    
    Creates a span for background task execution with proper error handling.
    
    Args:
        name: Optional name for the span. If not provided, uses function name.
        **kwargs: Additional keyword arguments (for compatibility with New Relic API)
    
    Returns:
        Decorated function with OpenTelemetry tracing
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **func_kwargs) -> Any:
            # Get the tracer
            tracer = trace.get_tracer(__name__)
            
            # Determine span name
            span_name = name or f"{func.__module__}.{func.__name__}"
            
            # Create and start span
            with tracer.start_as_current_span(
                span_name,
                kind=trace.SpanKind.INTERNAL,
                attributes={
                    "task.type": "background",
                    "function.name": func.__name__,
                    "function.module": func.__module__,
                }
            ) as span:
                try:
                    # Add function arguments as attributes (be careful with sensitive data)
                    if args:
                        span.set_attribute("task.args_count", len(args))
                    if func_kwargs:
                        span.set_attribute("task.kwargs_count", len(func_kwargs))
                    
                    # Execute the function
                    result = func(*args, **func_kwargs)
                    
                    # Mark span as successful
                    span.set_status(Status(StatusCode.OK))
                    
                    return result
                    
                except Exception as e:
                    # Record the exception in the span
                    span.record_exception(e)
                    span.set_status(Status(StatusCode.ERROR, str(e)))
                    
                    # Re-raise the exception
                    raise
        
        return wrapper
    return decorator


def trace_function(name: Optional[str] = None, **kwargs) -> Callable:
    """
    Generic function tracer for OpenTelemetry.
    
    Args:
        name: Optional name for the span. If not provided, uses function name.
        **kwargs: Additional keyword arguments
    
    Returns:
        Decorated function with OpenTelemetry tracing
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **func_kwargs) -> Any:
            tracer = trace.get_tracer(__name__)
            span_name = name or f"{func.__module__}.{func.__name__}"
            
            with tracer.start_as_current_span(
                span_name,
                kind=trace.SpanKind.INTERNAL,
                attributes={
                    "function.name": func.__name__,
                    "function.module": func.__module__,
                }
            ) as span:
                try:
                    result = func(*args, **func_kwargs)
                    span.set_status(Status(StatusCode.OK))
                    return result
                except Exception as e:
                    span.record_exception(e)
                    span.set_status(Status(StatusCode.ERROR, str(e)))
                    raise
        
        return wrapper
    return decorator
