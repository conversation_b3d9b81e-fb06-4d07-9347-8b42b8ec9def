import logging
import time

import newrelic
from flask.cli import with_appcontext
from tenant_gateway.infrastructure.telemetry.decorators import background_task
from sentry_sdk.integrations.serverless import serverless_function
from tenant_gateway.application.services.dtos.rackrate_dto import HotelRatesDTO
from tenant_gateway.common.decorators import worker_middleware
from tenant_gateway.infrastructure.consumers.base_consumer import BaseRMQConsumer
from tenant_gateway.infrastructure.consumers.consumer_config import RackrateConfig
logger = logging.getLogger(__name__)
logger.setLevel(logging.CRITICAL)
class RackrateConsumer(BaseRMQConsumer):
    def __init__(self, rackrate_sync_service, tenant_id, service_code=None):
        super().__init__(RackrateConfig(tenant_id, service_code))
        logger.info("Listening to RMQ on host: %s from queue: %s", self.connection, self.queue)
        self.rackrate_sync_service = rackrate_sync_service
        self.service_code = service_code
    @serverless_function
    @worker_middleware
    @with_appcontext
    @background_task(name='rackrate_sync_worker')
    def process_message(self, body, message):
        start_time = time.time()
        try:
            if body.get('event') == 'RATE_UPDATED':
                self.process_rate_update_message(body)
        except Exception as exc:
            logger.error(f"Error in consuming the messages from Rackrate: {exc}, event {body}")
            message.reject()
            return
        logger.info(
            f"Rackrate message process complete. time taken for process {time.time() - start_time} "
            f"for event type {body.get('event')}")
        message.ack()
        logger.info(f"Rackrate Message Acknowledged")
    def process_rate_update_message(self, body):
        payload_data = body.get('data')
        hotel_rates_dto = HotelRatesDTO.from_dict(payload_data)
        for hotel_id, rates in hotel_rates_dto.hotel_rates.items():
            self.rackrate_sync_service.sync_hotel_rates(hotel_id, rates, self.service_code)
