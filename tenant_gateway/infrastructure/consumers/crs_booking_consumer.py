import logging
import os


from flask.cli import with_appcontext
from sentry_sdk.integrations.serverless import serverless_function
from treebo_commons.multitenancy.tenant_client import TenantClient
from ths_common.constants.booking_constants import BookingActions, ActionEntities, BookingSubResources

from tenant_gateway.common.decorators import worker_middleware
from tenant_gateway.domain.external_services.su.constants import SUB_CHANNEL_MAPPING
from tenant_gateway.infrastructure.consumers.base_consumer import BaseRMQConsumer
from tenant_gateway.infrastructure.consumers.consumer_config import CRSConfig
from tenant_gateway.infrastructure.telemetry.decorators import background_task

logger = logging.getLogger(__name__)


class CRSBookingConsumer(BaseRMQConsumer):

    def __init__(self, tenant_gateway_service, inventory_sync_service, tenant_id=TenantClient.get_default_tenant(),
                 service_code=None):
        super().__init__(CRSConfig(tenant_id=tenant_id, service_code=service_code, routing_keys=['booking'],
                                   queue_name=f'crs-booking-event-queue-{service_code}'))
        logger.info("Listening to RMQ on host: %s from queue: %s", self.connection, self.queue)
        self.inventory_sync_service = inventory_sync_service
        self.tenant_gateway_service = tenant_gateway_service
        self.service_code = service_code

    @serverless_function
    @worker_middleware
    @with_appcontext
    @background_task(name='crs_booking_consumer')
    def process_message(self, body, message):
        consumer_event = body
        logger.info("CRS booking process message called for message: %s", consumer_event.get('message_id'))
        try:
            if "booking" in consumer_event.get('event_type'):
                self.process_booking_event(consumer_event)
            elif (
                consumer_event.get("event_type") == "billing.updated"
                and consumer_event.get("user_action") in ["add_allowance", "update_allowance"]
            ):
                self.process_allowance_event(consumer_event)

        except Exception as exc:
            logger.exception(f"Unable to process crs message due to exception: {exc}")
            message.reject()
            return

        logger.info("CRS Booking Message Processed")
        message.ack()
        logger.info("CRS Booking Message Acknowledged")

    def process_booking_event(self, consumer_event):
        if not consumer_event.get('events'):
            return

        for event in consumer_event['events']:
            if event.get('payload') and event.get('entity_name') == ActionEntities.BOOKING.value:
                break
        else:
            return

        if consumer_event.get('root_application') == os.environ.get('APP_NAME'):
            # Ignore booking event created by action from tenant gateway app
            # to avoid infinite loop of requests to external system
            return

        hotel_id = event['payload'].get('hotel_id')
        booking_id = event['payload'].get('booking_id')
        if booking_id == "3175-5226-6066":
            return

        if "booking.created" in consumer_event.get('event_type'):
            self.tenant_gateway_service.create_booking_from_crs_event(
                self.service_code,
                hotel_id,
                event['payload'],
                consumer_event.get('user_action'),
            )
        elif "booking.updated" in consumer_event.get('event_type'):
            self.process_booking_updated_event(consumer_event, hotel_id, event['payload'])

    def process_booking_updated_event(self, consumer_event, hotel_id, booking_event):
        # This contains the room stay ids which got updated/cancelled/reverse_cancelled in a multi room bookings
        room_update_event = self._check_for_room_stay_update(consumer_event)
        if consumer_event.get('user_action') == BookingActions.CANCEL.value:
            self.tenant_gateway_service.cancel_booking_from_crs_event(
                self.service_code,
                hotel_id,
                booking_event,
                room_update_event,
                consumer_event.get('user_action'),
            )
            return
        elif consumer_event.get('user_action') == BookingActions.NOSHOW.value:
            self.process_booking_noshow(consumer_event)

        self.tenant_gateway_service.update_booking_from_crs_event(
            self.service_code,
            hotel_id,
            booking_event,
            room_update_event,
            consumer_event.get('user_action'),
        )

    @staticmethod
    def _check_for_room_stay_update(consumer_event):
        for event in consumer_event['events']:
            if event.get('payload') and event.get('entity_name') == BookingSubResources.ROOM_STAY:
                return event['payload']
        return None

    def process_booking_noshow(self, consumer_event):
        if not (consumer_event.get('events') and consumer_event.get('user_action') == BookingActions.NOSHOW.value):
            return
        for event in consumer_event['events']:
            if not (event.get('payload') and event.get('entity_name') == ActionEntities.BOOKING.value):
                return
            hotel_id = event['payload'].get('hotel_id')
            booking_id = event['payload'].get('booking_id')
            if event['payload'].get('status') == BookingActions.NOSHOW.value:
                if not event['payload']['source'].get('subchannel_code') == SUB_CHANNEL_MAPPING.get("booking.com"):
                    self.inventory_sync_service.update_noshow(hotel_id, booking_id, service_code=self.service_code)

    def process_allowance_event(self, consumer_event):
        if not consumer_event.get('events'):
            return

        for event in consumer_event['events']:
            if event.get("payload") and event.get("entity_name") == "bill":
                break
        else:
            return

        if consumer_event.get('root_application') == os.environ.get('APP_NAME'):
            # Ignore booking event created by action from tenant gateway app
            # to avoid infinite loop of requests to external system
            return

        hotel_id = event['payload'].get('vendor_id')
        self.tenant_gateway_service.process_allowance_event_from_crs(
            self.service_code,
            hotel_id,
            event['payload'],
            consumer_event.get('user_action'),
        )
