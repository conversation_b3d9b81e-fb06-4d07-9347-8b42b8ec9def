import logging


from flask.cli import with_appcontext
import sentry_sdk
from sentry_sdk.integrations.serverless import serverless_function
from ths_common.constants.catalog_constants import RoomTypeStatus
from treebo_commons.multitenancy.tenant_client import TenantClient

from tenant_gateway.common.constants import CatalogEvents
from tenant_gateway.common.decorators import worker_middleware
from tenant_gateway.common.exceptions import ChannelManagerNotEnabledException, CatalogEventConsumptionError
from tenant_gateway.infrastructure.consumers.base_consumer import BaseRMQConsumer
from tenant_gateway.infrastructure.consumers.consumer_config import CatalogConfig
from tenant_gateway.infrastructure.external_clients.catalog_service.catalog_dtos import (
    RoomRackRateDto,
    HotelRoomTypeConfigDto, CatalogHotelDto,
)
from tenant_gateway.infrastructure.telemetry.decorators import background_task

logger = logging.getLogger(__name__)


class CatalogServiceConsumer(BaseRMQConsumer):
    def __init__(self, catalog_sync_service, tenant_id=TenantClient.get_default_tenant(), service_code=None):
        super().__init__(CatalogConfig(tenant_id, service_code))
        logger.info("Listening to RMQ on host: %s from queue: %s", self.connection, self.queue)
        self.catalog_sync_service = catalog_sync_service
        self.service_code = service_code

    @serverless_function
    @worker_middleware
    @with_appcontext
    @background_task(name='catalog_sync_worker')
    def process_message(self, body, message):
        obj = body
        logger.info("Catalog process_message called for entity: %s", obj.get('entity'))
        try:
            if obj.get('entity') == CatalogEvents.PROPERTY.value:
                self.process_property_message(obj)

            if obj.get('entity') == CatalogEvents.ROOM_TYPE_CONFIG.value:
                self.process_room_type_config_message(obj)

            if obj.get('entity') == CatalogEvents.ROOM_RACK_RATE.value:
                self.process_room_rack_rate(obj)

        except ChannelManagerNotEnabledException as exc:
            logger.error(f"Error in consuming the messages from catalog {exc}")
            return
        except Exception as exc:
            logger.info(f"Error in consuming the messages from catalog consumer: {exc}")
            sentry_sdk.capture_exception(exc)
            message.reject()
            return

        logger.info("Catalog Message Processed")
        message.ack()
        logger.info("Catalog Message Acknowledged")

    def process_room_rack_rate(self, obj):
        logger.info("Received room rack rate message for info: %s", obj.get('data'))
        room_rack_rate_dto = RoomRackRateDto.create_from_catalog_data(obj.get('data'))

        # trigger for creation of room in su
        if (
                obj.get('operation_type') in
                [CatalogEvents.OperationTypes.CREATE.value, CatalogEvents.OperationTypes.UPDATE.value]
        ):
            self.catalog_sync_service.create_or_update_room_type(self.service_code, room_rack_rate_dto)

    def process_property_message(self, obj):
        catalog_hotel_dto = CatalogHotelDto.create_from_catalog_data(obj.get('data'))
        if obj.get('operation_type') == CatalogEvents.OperationTypes.UPDATE.value:
            self.catalog_sync_service.update_hotel(self.service_code, catalog_hotel_dto)
        self.catalog_sync_service.process_property_message(self.service_code, obj)

    def process_room_type_config_message(self, obj):
        room_type_config_dto = HotelRoomTypeConfigDto.create_from_catalog_data(
            obj.get("cs_property_id"),
            obj.get("data")
        )
        if obj.get("operation_type") == CatalogEvents.OperationTypes.CREATE.value:
            self.catalog_sync_service.add_room_type_config(self.service_code, room_type_config_dto)
        if obj.get('operation_type') == CatalogEvents.OperationTypes.UPDATE.value:
            self.catalog_sync_service.update_room_type_config(self.service_code, room_type_config_dto)
        if obj.get('operation_type') == CatalogEvents.OperationTypes.DELETE.value:
            room_type_config_dto.status = RoomTypeStatus.INACTIVE
            self.catalog_sync_service.delete_room_type_config(self.service_code, room_type_config_dto)
