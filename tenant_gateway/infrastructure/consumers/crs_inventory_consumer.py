import logging
import os

from flask.cli import with_appcontext
from tenant_gateway.infrastructure.telemetry.decorators import background_task
from sentry_sdk.integrations.serverless import serverless_function
from treebo_commons.multitenancy.tenant_client import TenantClient
from tenant_gateway.common.decorators import worker_middleware
from tenant_gateway.infrastructure.consumers.base_consumer import BaseRMQConsumer
from tenant_gateway.infrastructure.consumers.consumer_config import CRSConfig
logger = logging.getLogger(__name__)
class CRSInventoryConsumer(BaseRMQConsumer):
    def __init__(self, inventory_sync_service, tenant_id=TenantClient.get_default_tenant(),
                 service_code=None):
        super().__init__(CRSConfig(tenant_id=tenant_id, exchange_type='x-consistent-hash', service_code=service_code,
                                   routing_keys=['1'], queue_name=self._get_queue_name(service_code),
                                   args={'hash-header': 'hash-on'}, exchange_name=f'crs-inventory-hash-exchange-{service_code}'))
        logger.info("Listening to RMQ on host: %s from queue: %s", self.connection, self.queue)
        self.inventory_sync_service = inventory_sync_service
        self.service_code = service_code
    @serverless_function
    @worker_middleware
    @with_appcontext
    @background_task(name='crs_inventory_consumer')
    def process_message(self, body, message):
        consumer_event = body
        logger.info("CRS inventory process message called for message: %s", consumer_event.get('message_id'))
        try:
            if "inventory" in consumer_event.get('event_type'):
                event_id = consumer_event['message_id']
                if consumer_event['events']:
                    self.inventory_sync_service.sync_inventories_via_crs_consumer(consumer_event, event_id,
                                                                                  self.service_code)
        except Exception as exc:
            logger.exception(f"Unable to process crs message due to exception: {exc}")
            message.reject()
            return
        logger.info("CRS Inventory Message Processed")
        message.ack()
        logger.info("CRS Inventory Message Acknowledged")
    @staticmethod
    def _get_queue_name(service_code):
        queue_id = os.environ.get('CRS_INVENTORY_HASH_EXCHANGE_QUEUE_ID') or 1
        return f'crs-inventory-event-queue-{service_code}-{queue_id}'
