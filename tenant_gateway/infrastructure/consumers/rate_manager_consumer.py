import logging
import time

from flask.cli import with_appcontext
from tenant_gateway.infrastructure.telemetry.decorators import background_task
from sentry_sdk.integrations.serverless import serverless_function
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.utils import dateutils
from tenant_gateway.common.constants import RateManagerEvents, TREEBO_RATE_PLAN_CODES, ServiceCodes
from tenant_gateway.common.decorators import worker_middleware
from tenant_gateway.common.exceptions import ChannelManagerNotEnabledException
from tenant_gateway.infrastructure.consumers.base_consumer import BaseRMQConsumer
from tenant_gateway.infrastructure.consumers.consumer_config import RateManagerConfig
from tenant_gateway.infrastructure.external_clients.rate_manager_service.rate_manager_dtos import RatePlanDtoV2, \
    RatePlanRateDto
logger = logging.getLogger(__name__)
logger.setLevel(logging.CRITICAL)
class RateManagerConsumer(BaseRMQConsumer):
    def __init__(self, rate_manager_sync_service, tenant_gateway_service, tenant_id=TenantClient.get_default_tenant(),
                 service_code=None):
        super().__init__(RateManagerConfig(tenant_id, service_code))
        logger.info("Listening to RMQ on host: %s from queue: %s", self.connection, self.queue)
        self.rate_manager_sync_service = rate_manager_sync_service
        self.tenant_gateway_service = tenant_gateway_service
        self.service_code = service_code
    @serverless_function
    @worker_middleware
    @with_appcontext
    @background_task(name='rate_manager_sync_worker')
    def process_message(self, body, message):
        start_time = time.time()
        try:
            # TODO: Move hotel rate plan mappings upstream for rate_plan_created_message & rate_plan_updated_message
            if body.get('event') == RateManagerEvents.RATE_PLAN_CREATED.value:
                self.process_rate_plan_created_message(body)
            if body.get('event') == RateManagerEvents.RATE_PLAN_UPDATED.value:
                self.process_rate_plan_updated_message(body)
            if body.get('event') == RateManagerEvents.RATE_PLAN_RATE_UPDATED.value:
                self.process_rate_plan_rate_updated_message(body)
        except ChannelManagerNotEnabledException as exc:
            logger.error(f"Error in consuming the messages from rate manager {exc}")
            return
        except Exception as exc:
            logger.info(f"Error in consuming the messages from rate manager: {exc}, event {body}")
            message.reject()
        logger.info(
            f"Rate manager message process complete. time taken for process {time.time() - start_time} "
            f"for event type {body.get('event')}")
        message.ack()
        if (time.time() - start_time) >= 500:
            logger.info(f"Rate Manager Event Body: {body} Took {time.time() - start_time} seconds to process.")
            f"Rate manager Message acknowledged, time taken {time.time() - start_time} "
    def process_rate_plan_created_message(self, obj):
        logger.info(f"Create Rate plan event received with body: {obj}")
        rate_plan_dto = RatePlanDtoV2.create_from_payload(obj.get('payload').get('rate_plan'))
        # skipping some treebo website specific rate plans creation
        if (
                rate_plan_dto.short_code.strip() in TREEBO_RATE_PLAN_CODES
                and self.service_code == ServiceCodes.SU.value
        ):
        self.rate_manager_sync_service.create_rate_plan(rate_plan_dto, self.service_code)
        self.rate_manager_sync_service.push_rates_for_rate_plan(rate_plan_dto, self.service_code)
    def process_rate_plan_updated_message(self, obj):
        # skipping some treebo website specific rate plans update
        self.rate_manager_sync_service.update_rate_plan(rate_plan_dto, self.service_code)
        # if rate_plan_dto.is_active:
        #     self.rate_manager_sync_service.push_rates_for_rate_plan(rate_plan_dto)
    def process_rate_plan_rate_updated_message(self, obj):
        rate_plan_rate_dtos = []
        if obj.get('payload').get('rate_plan_rates'):
            rate_plan_rate_dtos.extend(
                [RatePlanRateDto.create_from_payload(rate_plan_rate)
                 for rate_plan_rate in obj.get('payload').get('rate_plan_rates')]
            )
        if obj.get('payload').get('running_rate_plan_rates'):
                 for rate_plan_rate in obj.get('payload').get('running_rate_plan_rates')]
        if rate_plan_rate_dtos:
            # Ignoring past rate plan rate updates
            filtered_rate_plan_rates = []
            for rate_plan_rate_dto in rate_plan_rate_dtos:
                if (
                        rate_plan_rate_dto.start_date and
                        dateutils.ymd_str_to_date(rate_plan_rate_dto.start_date) >= dateutils.current_date()
                ) or (
                        rate_plan_rate_dto.stay_date and
                        dateutils.ymd_str_to_date(rate_plan_rate_dto.stay_date) >= dateutils.current_date()
                ):
                    filtered_rate_plan_rates.append(rate_plan_rate_dto)
            if filtered_rate_plan_rates:
                self.rate_manager_sync_service.push_rate_plan_rate_update(filtered_rate_plan_rates, self.service_code)
    @staticmethod
    def _make_cache_key(base_rate_dto):
        return get_current_tenant_id() + '.' + str(base_rate_dto)
